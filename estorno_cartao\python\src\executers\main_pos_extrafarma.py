from services.mercado_pago_services import ServiceMercadoPago
from services.movimento_service import MovimentoService
from services.planoAssinatura import PlanoAssinatura
from services.estorno_service import EstornoService
from services.vtex_api_service import VTEXHandler
from services.vtex.vtex_validations import VtexValidator
from services.getnet_service import ServiceGetnet
from services.cielo_service import ServiceCielo
from services.nupay.nupay_services import ServiceNuPay
from services.pagaleve.paga_leve_service import process_pagaleve_refund
from models.card_refund_info import CardRefundInfo
import Config.vars as vars
from services.multi_payment_method_sale.multi_payment_method_service import get_card_refund_info_pos
from Connections.search_db import SearchDb
from services.multi_report_handler import *
from services.lio_service import LioService
from datetime import datetime
from services.sms import SMS  
import pandas as pd
import utils
import json
from aux_temp import register_refund_activity
from services.logger import create_logger
from services.refund_api.refunds_info import Refund<PERSON>pi
from typing import Dict, List

exf_non_tef_logger = create_logger(__name__, without_handler = True)


class POSEX():
    
    def __init__(self, config):
   
        self.mercadopago_services = ServiceMercadoPago(config)
        self.movimento_service = MovimentoService(config)
        self.estorno_service = EstornoService(config)
        self.getnet_services = ServiceGetnet(config)
        self.service_cielo = ServiceCielo(config)
        self.lio_sevices = LioService(config)
        self.ids_estorno_to_skip = []
        self.sms_sevices = SMS(config)
        self.search = SearchDb()           
        self.config = config


        super().__init__()
    
    def execute_process(self, requested_refund: List[Dict] = None):
                
        log_report("#####################")
        log_report("Iniciando Estorno POS EXTRAFARMA")
        log_report("#####################")

        vtex_service = VTEXHandler(self.config['api']['vtex'])

        
        auth_token = PlanoAssinatura.auth_token()

        if requested_refund is None:
            new_refunds = RefundApi().get_not_processed_refunds_by_days_back_and_pos_kind(15, vars.POS_CASE, vars.EXTRA_FARMA_CASE)
            if new_refunds.failure: raise SystemExit(new_refunds.error_description)  
            movimento = new_refunds.data
        else:
            movimento = requested_refund

        for venda in movimento:

            if not isinstance(venda, dict): 
                msg = f"ATENÇÃO: pedido de estorno id_estorno_fin: {venda.data['ID_ESTORNO']} com erro na ORM"
                exf_non_tef_logger.warning(msg)
                log_report(msg) 
                continue

            if venda['numeroPedidoVTEX'] != '' or venda['numeroPedidoVTEX'] != None:
                venda = self.__autalizar_data_venda(venda)
            
            if (venda['bandeira'] == '' or venda['bandeira'] is None): 
                venda = utils.fill_lacking_information(venda)

            filial_origem = venda['codigoFilialOrigem']
            cnpj_filial_origem = venda['cnpjFilialOrigem']    
            filial_destino = venda['codigoFilialDestino']
            cnpj_filial_destino = venda['cnpjFilialDestino']
            numero_cupom = venda['numeroCupom']
            numero_coo = venda['numeroCoo']
            pdv = venda['pdvDevolucao']
            nome_cliente = venda['nomeCliente']
            numero_pedido_vtex = venda['numeroPedidoVTEX']
            numero_pedido_delivery = venda['numeroPedidoDelivery']
            data_venda_movimento = venda['dataMovimento']
            valor_venda_movimento = venda['valorVenda']
            valor_estorno = venda['valorEstorno']
            data_devolucao = venda['dataDevolucao'] if venda['dataDevolucao'] != '0001-01-01T00:00:00' else None
            nsu_movimento_tef = venda['codigoNSUTEF']
            nsu_movimento_host = venda['codigoNSUHOST']
            bandeira_cartao_movimento = venda['bandeira']
            telefone = venda['telefoneCliente']
            email = venda['emailCliente']
            numero_pre_venda = venda['numeroPreVenda']
            plano_assinatura = venda['planoAssinatura']
            canal_venda = venda['canalVendas']
            cartao_sempre = venda['cartaoSempre']
            parcelas = venda['quantParcelas']
            tipo_venda = venda['tipo']
            flag_pag_uni = venda['flagPagamentoUnificado']
            nsu_pos = venda['nsuPos']
            id_troca_cab = venda['idTrocaCab']
            coo_pag_uni = venda['cooPagUni']
            id_estorno = venda['idEstorno']
            new_data = data_venda_movimento.split('/')
            nome_empresa = "Extrafarma"

            if utils.avoid_legacy_api_duplicates(id_estorno): continue
            
            if 'statusVenda' in venda:
                if venda['statusVenda'] == 'REPROCESSAMENTO':
                    flagrepro = 0
                    instancia = venda['instancia']
                else: 
                    flagrepro = -1  
                    instancia = -1 
            else:
                 flagrepro = -2  
                 instancia = -2 


            obj_estorno_log = {
                "codigoFilialOrigem": filial_origem,
                "cnpjFilialOrigem": cnpj_filial_origem,
                "codigoFilialDestino": filial_destino,
                "cnpjFilialDestino": cnpj_filial_destino,
                "numeroCupom": numero_cupom,
                "numeroCoo": numero_coo,
                "nomeCliente": nome_cliente,
                "pdvInformado": pdv,
                "numeroPedidoVTEX": numero_pedido_vtex,
                "numeroPedidoDelivery": numero_pedido_delivery,
                "dataMovimento": '{}-{}-{}'.format(new_data[2],new_data[1],new_data[0]),
                "valorTotalVenda": valor_venda_movimento,
                "valorEstorno": valor_estorno,
                "dataDevolucao":data_devolucao,
                "nsutef": nsu_movimento_tef,
                "nsuhost": nsu_movimento_host,
                "bandeira": bandeira_cartao_movimento,
                "telefone": telefone,
                "email": email,
                "numeroPreVenda": numero_pre_venda,
                "planoAssinatura": plano_assinatura,
                "canalVendas": canal_venda,                
                "cartaoSempre": cartao_sempre,
                "parcelas": parcelas,
                "tipo": tipo_venda,
                "flagPagamentoUni": flag_pag_uni,
                "nsuPos": nsu_pos,
                "idDevtrocaCab": id_troca_cab,
                "cooPagUni": coo_pag_uni,
                "idEstorno": id_estorno,
                "lote":self.__montar_lote('{}-{}-{}'.format(new_data[2],new_data[1],new_data[0])),                              
                "tipoEstorno":"Estorno POS",
                "nomeEmpresa":nome_empresa,
                "flagrepro":flagrepro,
                "instancia": instancia,
            }
            

            tipo_pagamento = utils.is_a_mapped_payment_method(bandeira_cartao_movimento, 
                                                                self.estorno_service, 
                                                                obj_estorno_log)
            
            if tipo_pagamento == -1: continue
            
            if tipo_venda == '' or tipo_venda == None:
                obj_estorno_log['motivo'] = 'Venda PDV Antigo estonanda no troca e devolução'
                obj_estorno_log['status'] = vars.STATUS_REFUND_PROCESS_END
                               
                self.estorno_service.record_refund_processment(obj_estorno_log)
                log_report("Estorno {}:Venda PDV Antigo estonanda no troca e devolução".format(id_estorno))
                continue

            if plano_assinatura == 'S':
                
                retorno = PlanoAssinatura.check_assinatura(numero_pre_venda,auth_token)
                
                if retorno[2:9] == 'sucesso':
                     print('...Assinatura Cancelada com Sucesso...')
                     obj_estorno_log['motivo'] = 'Assinatura Cancelada' 
                     obj_estorno_log['status'] = vars.STATUS_REFUND_PROCESS_END
                     self.estorno_service.record_refund_processment(obj_estorno_log)
                     log_report("Estorno {}:Assinatura Cancelada".format(id_estorno))
                     continue
                     
                else:
                     print('...Venda nao localizada na SCOPE...')
                     obj_estorno_log['motivo'] = 'Não foi possivel Cancelar a Assinatura' 
                     obj_estorno_log['status'] = vars.STATUS_PENDENCY_FOR_CSC   
                     self.estorno_service.record_refund_processment(obj_estorno_log)
                     log_report("Estorno {}:Não foi possivel Cancelar a Assinatura".format(id_estorno))
                     continue

            if tipo_pagamento == 'pix':
                
                #VELOCE
                if numero_pedido_vtex == None or numero_pedido_vtex == "":
                    

                    token_lio = self.lio_sevices.get_token() 
                    cnpjformatado = self.tratarnum(cnpj_filial_origem)
                    dados_veloce = self.lio_sevices.consultar_vendas(data_venda_movimento,cnpjformatado[1:],numero_cupom,token_lio)
                    
                    if dados_veloce == -1:
                        obj_estorno_log['motivo'] = 'Venda Pix Não localizada na Veloce POS'
                        obj_estorno_log['status'] = vars.STATUS_PENDENCY_FOR_CSC                       
                        self.estorno_service.record_refund_processment(obj_estorno_log)
                        log_report("Estorno {}:Venda Pix Não localizada na Veloce POS".format(id_estorno))
                        continue

                    

                    #Se não tem na vtex, mas tem na veloce
                    tipo = 'tef_ext'
                    
                    obj_estorno_log['nsutef'] = nsu = dados_veloce['pagamentos'][0][0]['order_id']
                    obj_estorno_log['nsuhost'] = nsu = dados_veloce['pagamentos'][0][0]['order_id']

                #VTEX
                else:

                    vtex_referee  = VtexValidator(obj_estorno_log)
                    vtex_sale_info, obj_estorno_log = vtex_referee.get_vtex_info()

                    if vtex_sale_info == -1:
                        register_refund_activity(obj_estorno_log, self.estorno_service) 
                        continue
                
                    tipo = 'pos_ext'
                    obj_estorno_log['nsutef'] = vtex_sale_info['nsu']
                    obj_estorno_log['nsuhost'] = vtex_sale_info['nsu']
                    
                    
                    if obj_estorno_log['bandeira'].lower() in utils.PIX_PAGA_LEVE_LABELS:

                        _, refund_info = process_pagaleve_refund(vtex_sale_info["nsu"], obj_estorno_log)
                        register_refund_activity(refund_info, self.estorno_service) 
                        continue


                obj_estorno_log['bandeira'] = 'Pix'
                    

                estorno = self.mercadopago_services.estornopix(obj_estorno_log['nsutef'],valor_venda_movimento,valor_estorno,tipo)
                
                if estorno == -1:
    
                    obj_estorno_log['motivo'] = 'codigo nsu invalido no Mercado Pago' 
                    obj_estorno_log['status'] = vars.STATUS_PENDENCY_FOR_CSC
                    self.estorno_service.record_refund_processment(obj_estorno_log)
                    log_report("Estorno {}:codigo nsu invalido no Mercado Pago".format(id_estorno))
                    
                    continue
                    
                elif estorno == -2:
                    
                    obj_estorno_log['motivo'] = 'Estorno ja consta como REALIZADO' 
                    obj_estorno_log['status'] = vars.STATUS_REFUND_PROCESS_END 
                    self.estorno_service.record_refund_processment(obj_estorno_log)
                    log_report("Estorno {}:Estorno ja consta como REALIZADO".format(id_estorno))
                    
                    continue

                elif estorno == -20:
                    error_description = "venda desse estorno consta como rejeitada"
                    msg = f"{vars.BASE_MSG.format(id_estorno=id_estorno)}{error_description}"
                    obj_estorno_log['motivo'] = error_description
                    obj_estorno_log['status'] = vars.STATUS_REFUND_PROCESS_END 
                    self.estorno_service.record_refund_processment(obj_estorno_log)
                    log_report(msg)
                    exf_non_tef_logger.info(msg)
                    continue
                    
                elif estorno == -3:
                    
                    obj_estorno_log['motivo'] = 'Estorno ja consta como Parcialmete REALIZADO' 
                    obj_estorno_log['status'] = vars.STATUS_REFUND_PROCESS_END
                    self.estorno_service.record_refund_processment(obj_estorno_log)
                    log_report("Estorno {}:Estorno ja consta como Parcialmete REALIZADO".format(id_estorno))
                    continue

                elif estorno == -30:
                    error_description = "status de pix mercado pago não mapeada"
                    msg = f"{vars.BASE_MSG.format(id_estorno=id_estorno)}{error_description}"
                    obj_estorno_log['motivo'] = error_description
                    obj_estorno_log['status'] = vars.STATUS_PENDENCY_FOR_CSC
                    self.estorno_service.record_refund_processment(obj_estorno_log)
                    log_report(msg)
                    exf_non_tef_logger.info(msg)
                    continue
                    
                elif estorno == -4:

                    obj_estorno_log['motivo'] = 'Valor do estonar Maior que o valor total da Venda' 
                    obj_estorno_log['status'] = vars.STATUS_PENDENCY_FOR_CSC
                    self.estorno_service.record_refund_processment(obj_estorno_log)
                    log_report("Estorno {}:Valor do estonar Maior que o valor total da Venda".format(id_estorno))
                    
                    continue

                elif estorno == -5:
                    obj_estorno_log['motivo'] = 'REALIZADO'
                    obj_estorno_log['status'] = vars.STATUS_REFUND_PROCESS_END
                    self.estorno_service.record_refund_processment(obj_estorno_log)
                    self.sms_sevices.sendsms(telefone,cartao_sempre,valor_estorno)
                    log_report("Estorno {}:REALIZADO".format(id_estorno))
                    
                    continue

                elif estorno == -6:
                    obj_estorno_log['motivo'] = 'Venda se encontra com Status de Cancelada na Mercado Pago'
                    obj_estorno_log['status'] = vars.STATUS_PENDENCY_FOR_CSC
                    self.estorno_service.record_refund_processment(obj_estorno_log)                   
                    log_report("Estorno {}:Venda se encontra com Status de Cancelada na Mercado Pago".format(id_estorno))
                    
                    continue

                elif estorno == -11:
                    obj_estorno_log['motivo'] = 'Venda Pix passou do prazo de 60 dias.'
                    obj_estorno_log['status'] = vars.STATUS_PENDENCY_FOR_CSC
                    self.estorno_service.record_refund_processment(obj_estorno_log)                   
                    log_report("Estorno {}:Venda Pix passou do prazo de 60 dias.".format(id_estorno))
                    continue

                else:
                    error_description = "resposta de procesamento de estorno pix mercado pago não mapeada"
                    msg = f"{vars.BASE_MSG.format(id_estorno=id_estorno)}{error_description}"
                    obj_estorno_log['motivo'] = error_description
                    obj_estorno_log['status'] = vars.STATUS_PENDENCY_FOR_CSC
                    self.estorno_service.record_refund_processment(obj_estorno_log)                   
                    log_report(msg)
                    exf_non_tef_logger.info(msg)
                    continue
                        
            
            if tipo_pagamento == 'cartao':

                today = datetime.today()          
                datastr = '{}-{}-{}'.format(new_data[2],new_data[1],new_data[0])         
                data = pd.to_datetime(datastr) + pd.DateOffset(hours = 32)

                if (data >= today):
                    print('Venda lançada no mesmo dia')              
                    continue

                if numero_pedido_vtex == None or numero_pedido_vtex == "":

                    print("veloce_pos")
                   
                    cnpjformatado = self.tratarnum(cnpj_filial_origem)
                    token_lio = self.lio_sevices.get_token() 
                    dados_veloce = self.lio_sevices.consultar_vendas(data_venda_movimento,cnpjformatado[1:],numero_cupom,token_lio)
                    
                    if dados_veloce == -1:
                        print('...Venda nao localizada na VELOCE...')
                        obj_estorno_log['motivo'] = 'Venda nao localizada na veloce' 
                        obj_estorno_log['status'] = vars.STATUS_PENDENCY_FOR_CSC   
                        self.estorno_service.record_refund_processment(obj_estorno_log) 
                        log_report("Estorno {}:Venda nao localizada na veloce".format(id_estorno))
                        continue

                    
                    pos_acquire_info = dados_veloce['pagamentos'][0][0]['json_pagamento']
                    veloce_general_info = dados_veloce['pagamentos'][0][0]

                    if pos_acquire_info == None or pos_acquire_info == "":
                        print('...Venda não possui dados de Pagamento...')
                        obj_estorno_log['motivo'] = 'Venda sem dados de Pagamento' 
                        obj_estorno_log['status'] = vars.STATUS_PENDENCY_FOR_CSC   
                        self.estorno_service.record_refund_processment(obj_estorno_log)
                        log_report("Estorno {}:Venda sem dados de Pagamento".format(id_estorno)) 
                        continue

                    jsonpag = json.loads(pos_acquire_info)
                
                    if 'mask' in jsonpag:               
                        obj_estorno_log['n_cartao'] = jsonpag['mask']
                
                    else:
                        print('...Venda não possui cartao mascarado...')
                        obj_estorno_log['motivo'] = 'Venda não possui cartao mascarado' 
                        obj_estorno_log['status'] = vars.STATUS_PENDENCY_FOR_CSC   
                        self.estorno_service.record_refund_processment(obj_estorno_log) 
                        log_report("Estorno {}:Venda não possui cartao mascarado".format(id_estorno)) 
                        continue

                    try: 
                        obj_estorno_log = utils.verify_diff_values(obj_estorno_log, dados_veloce['pagamentos'][0][0]['valor'])
                    except utils.DiffValueError:
                        obj_estorno_log['motivo'] = 'Divergencia de valores entre Troca devolução e outras fontes (VTEX ou VDA)'
                        obj_estorno_log['status'] = vars.STATUS_PENDENCY_FOR_CSC    
                        self.estorno_service.record_refund_processment(obj_estorno_log)
                        log_report("Estorno {}:Divergencia de valores entre Troca devolução e outras fontes (VTEX ou VDA)".format(id_estorno))
                        continue


                    card_requesition_info, refund_info = get_card_refund_info_pos(obj_estorno_log, veloce_general_info)
                    obj_estorno_log["codigoAutorizacao"] = refund_info.codigoAutorizacao
                    obj_estorno_log["nsuhost"] = refund_info.nsuhost
                    obj_estorno_log["nsutef"] = refund_info.nsutef

                    if isinstance(card_requesition_info, vars.ResultHandle):
                        register_refund_activity(refund_info, self.estorno_service) 
                        continue
                    
                    if utils.is_there_a_partial_refund_to_wait(obj_estorno_log): continue
                    
                    if veloce_general_info['adquirente'].lower() == vars.CIELO_ACQUIRE_NAME.lower(): 

                        protocolo = self.service_cielo.estornoCielo(card_requesition_info, vars.EXTRA_FARMA_CASE)
                    
                        if protocolo == -1: 

                            msg = "Erro ao pedir estorno na adquirente cielo"
                            obj_estorno_log['motivo'] = msg
                            obj_estorno_log['status'] = vars.STATUS_PENDENCY_FOR_CSC   
                            obj_estorno_log['adquirente'] = vars.CIELO_ACQUIRE_NAME 
                            self.estorno_service.record_refund_processment(obj_estorno_log)
                            log_report("Estorno {}: Erro ao pedir estorno na adquirente cielo".format(id_estorno))
                            continue

                        elif protocolo == 2:
                            continue

                        obj_estorno_log['protocoloCielo'] = protocolo
                        obj_estorno_log['status'] = vars.STATUS_WAIT_CIELO
                        obj_estorno_log['adquirente'] = vars.CIELO_ACQUIRE_NAME
                        obj_estorno_log['id_estorno_adquirente'] = protocolo
                        self.estorno_service.record_refund_processment(obj_estorno_log)
                        continue
                    
                    if veloce_general_info['adquirente'].lower() == vars.GETNET_ACQUIRE_NAME.lower():


                        protocolo = self.getnet_services.cancel_getnet_service(card_requesition_info, vars.EXTRA_FARMA_CASE)

                        if protocolo == -1: 
                            msg = "Erro ao pedir estorno na adquirente getnet"
                            obj_estorno_log['motivo'] = msg
                            obj_estorno_log['status'] = vars.STATUS_PENDENCY_FOR_CSC  
                            obj_estorno_log['adquirente'] = vars.GETNET_ACQUIRE_NAME                                          
                            self.estorno_service.record_refund_processment(obj_estorno_log)
                            log_report("Estorno {}:Venda não localizada para estorno".format(id_estorno))
                            continue
                        
                        obj_estorno_log['protocoloGetnet'] = protocolo
                        obj_estorno_log['status'] = vars.STATUS_WAIT_GETNET
                        obj_estorno_log['adquirente'] = vars.GETNET_ACQUIRE_NAME
                        obj_estorno_log['id_estorno_adquirente'] = protocolo
                        self.estorno_service.record_refund_processment(obj_estorno_log)
                        continue        

                else:
                    
                    dados_vtex = vtex_service.retrieve_data(obj_estorno_log)

                    if dados_vtex == -1:
                        obj_estorno_log['motivo'] = vars.ERROR_REASON_VTEX_REQUEST_RESTRY_TIMEOUT
                        obj_estorno_log['status'] = vars.STATUS_PENDENCY_FOR_CSC    
                        self.estorno_service.record_refund_processment(obj_estorno_log)
                        log_report(f"Estorno {id_estorno}: {vars.ERROR_REASON_VTEX_REQUEST_RESTRY_TIMEOUT}")               
                        continue

                    elif dados_vtex == -2:
                        obj_estorno_log['motivo'] = 'Códigos de autorização não encontrados na VTEX'
                        obj_estorno_log['status'] = vars.STATUS_PENDENCY_FOR_CSC    
                        self.estorno_service.record_refund_processment(obj_estorno_log)
                        log_report("Estorno {}: Códigos de autorização não encontrados na VTEX".format(id_estorno))
                        continue

                    elif dados_vtex == -3:
                        obj_estorno_log['motivo'] = 'Compra cancelada na VTEX'
                        obj_estorno_log['status'] = vars.STATUS_REFUND_PROCESS_END    
                        self.estorno_service.record_refund_processment(obj_estorno_log)
                        log_report("Estorno {}:Compra cancelada".format(id_estorno))
                        continue

                    elif dados_vtex == -4:
                        obj_estorno_log['motivo'] = 'Compra feita a dinheiro'
                        obj_estorno_log['status'] = vars.STATUS_REFUND_PROCESS_END    
                        self.estorno_service.record_refund_processment(obj_estorno_log)
                        log_report("Estorno {}:Compra feita a dinheiro".format(id_estorno))
                        continue
                    
                    elif dados_vtex == -8:
                        obj_estorno_log['motivo'] = 'Nao se trata de uma venda Pague Menos'
                        obj_estorno_log['status'] = vars.STATUS_PENDENCY_FOR_CSC    
                        self.estorno_service.record_refund_processment(obj_estorno_log)
                        log_report("Estorno {}:Compra feita a dinheiro".format(id_estorno))
                        continue

                    try: 
                        obj_estorno_log = utils.verify_diff_values(obj_estorno_log, dados_vtex['valor'])
                    except utils.DiffValueError:
                        obj_estorno_log['motivo'] = 'Divergencia de valores entre Troca devolução e outras fontes (VTEX ou VDA)'
                        obj_estorno_log['status'] = vars.STATUS_PENDENCY_FOR_CSC    
                        self.estorno_service.record_refund_processment(obj_estorno_log)
                        log_report("Estorno {}:Divergencia de valores entre Troca devolução e outras fontes (VTEX ou VDA)".format(id_estorno))
                        continue

                    obj_estorno_log['dataVenda'] = '{}-{}-{}'.format(new_data[2],new_data[1],new_data[0])
                    obj_estorno_log['n_cartao'] = dados_vtex['n_cartao']
                    obj_estorno_log['sequencial_controle'] = dados_vtex['sequencial_controle']
                    obj_estorno_log['tid'] = dados_vtex['t_id']
                    obj_estorno_log['codigoAutorizacao'] = dados_vtex['codigo_autorizacao']                     
                    obj_estorno_log['nsuhost'] = dados_vtex['nsu_settle'] if bool(dados_vtex['nsu_settle']) else dados_vtex['nsu']        

                    if utils.is_there_a_partial_refund_to_wait(obj_estorno_log): continue

                    if dados_vtex['adquirente'].lower() == vars.CIELO_ACQUIRE_NAME.lower():

                        card_requesition_info = CardRefundInfo(nsu_tef=obj_estorno_log['nsuhost'],
                                            nsu_external = obj_estorno_log['nsuhost'],
                                            establishment_code=dados_vtex["cod_estabelecimento"],
                                            acquirer_code=102,
                                            installments=obj_estorno_log['parcelas'],
                                            auth_code=obj_estorno_log['codigoAutorizacao'],
                                            payment_method_value=dados_vtex['valor'],
                                            refund_value=obj_estorno_log["valorEstorno"],
                                            sale_date=obj_estorno_log['dataVenda'])

                        protocolo = self.service_cielo.estornoCielo(card_requesition_info, vars.EXTRA_FARMA_CASE)
                        
                        if protocolo == -1: 

                            msg = "Erro ao pedir estorno na adquirente cielo"
                            obj_estorno_log['motivo'] = msg
                            obj_estorno_log['status'] = vars.STATUS_PENDENCY_FOR_CSC    
                            obj_estorno_log['adquirente'] = vars.CIELO_ACQUIRE_NAME
                            self.estorno_service.record_refund_processment(obj_estorno_log)
                            log_report("Estorno {}: Erro ao pedir estorno na adquirente cielo".format(id_estorno))
                            continue

                        elif protocolo == 2:
                            continue

                        obj_estorno_log['protocoloCielo'] = protocolo
                        obj_estorno_log['status'] = vars.STATUS_WAIT_CIELO
                        obj_estorno_log['adquirente'] = vars.CIELO_ACQUIRE_NAME
                        obj_estorno_log['id_estorno_adquirente'] = protocolo
                        self.estorno_service.record_refund_processment(obj_estorno_log)
                        continue
                        
                    if dados_vtex['adquirente'].lower() == vars.GETNET_ACQUIRE_NAME.lower():
                            
                        card_requesition_info = CardRefundInfo(nsu_tef=obj_estorno_log['nsuhost'],
                                        nsu_external = obj_estorno_log['nsuhost'],
                                        establishment_code=dados_vtex["cod_estabelecimento"],
                                        acquirer_code=108,
                                        installments=obj_estorno_log['parcelas'],
                                        auth_code=obj_estorno_log['codigoAutorizacao'],
                                        payment_method_value=dados_vtex['valor'],
                                        refund_value=obj_estorno_log["valorEstorno"],
                                        sale_date=obj_estorno_log['dataVenda'])
                

                        protocolo = self.getnet_services.cancel_getnet_service(card_requesition_info, vars.EXTRA_FARMA_CASE)
                        if protocolo == -1: 
                            msg = "Erro ao pedir estorno na adquirente getnet"
                            obj_estorno_log['motivo'] = msg
                            obj_estorno_log['status'] = vars.STATUS_PENDENCY_FOR_CSC 
                            obj_estorno_log['adquirente'] = vars.GETNET_ACQUIRE_NAME                                           
                            self.estorno_service.record_refund_processment(obj_estorno_log)
                            log_report(f"Estorno {id_estorno}: {msg}")
                            continue

                        obj_estorno_log['protocoloGetnet'] = protocolo
                        obj_estorno_log['status'] = vars.STATUS_WAIT_GETNET
                        obj_estorno_log['adquirente'] = vars.GETNET_ACQUIRE_NAME
                        obj_estorno_log['id_estorno_adquirente'] = protocolo
                        self.estorno_service.record_refund_processment(obj_estorno_log)
                        continue

            elif tipo_pagamento == 'hibrido':

                vtex_referee  = VtexValidator(obj_estorno_log)
                vtex_sale_info, obj_estorno_log = vtex_referee.get_vtex_info()

                if vtex_sale_info == -1:
                    register_refund_activity(obj_estorno_log, self.estorno_service) 
                    continue

                nupay = ServiceNuPay(self.config, "exf")
                _, obj_estorno_log = nupay.refund_sale(vtex_sale_info["t_id"], obj_estorno_log)

                register_refund_activity(obj_estorno_log, self.estorno_service) 
                continue

            else:
                obj_estorno_log['motivo'] = 'Erro não mapeado no RPA'
                obj_estorno_log['status'] = vars.STATUS_PENDENCY_FOR_CSC
                self.estorno_service.record_refund_processment(obj_estorno_log)
                log_report("URGENTE: Erro não mapeado no RPA") 
                continue  
        
        
        
        log_report("Fila POS extrafarma finalizada")   
        return 0

    
    def __montar_lote(self, data:str) -> str:
            
        if '-' in data:
            _data_aux = data.split('-')
            lote = datetime.strftime(datetime.strptime('{}/{}/{}'.format(_data_aux[2], _data_aux[1], _data_aux[0]), 
                                                        '%d/%m/%Y'), '%y%m%d')
        else: lote = datetime.strftime(datetime.strptime(data, '%d/%m/%Y'), '%y%m%d')

        return lote        

    def __autalizar_data_venda(self, dados_venda:dict) -> dict:        
        vtex_service = VTEXHandler(self.config['api']['vtex'])
        data = vtex_service.retrieve_creation_date(dados_venda['numeroPedidoVTEX'])
        if data == -1: return dados_venda
        dados_venda['dataMovimentoVtex'] = data
        return dados_venda


    def tratarnum(self,numVtex):        
        numero = ""
        for x in numVtex:
            if x.isdigit():
                numero = numero +""+ x
        return numero
    
  


    