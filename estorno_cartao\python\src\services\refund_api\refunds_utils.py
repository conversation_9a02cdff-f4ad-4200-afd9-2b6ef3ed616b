from typing import Dict, Union
from models.refund_log import EstornoL<PERSON>, turn_refundlog_data_obj_into_legacy_dict
from services.logger import create_logger
from models.result_handle import ResultHandle
from utils import traceback_of_last_n_lines

refund_utils_api_logger = create_logger(__name__, without_handler = True)


def legacy_api_orm(refund_raw_data: Dict) -> Union[Dict, ResultHandle]:

    try:
        
        refund_info = estorno_venda_orm_without_date_formatting(refund_raw_data)
        legacy_dict = turn_refundlog_data_obj_into_legacy_dict(refund_info)

        return legacy_dict
    
    except Exception as e:
        msg = f'Erro na conversão do objeto de id_estorno_fin:{refund_raw_data["ID_ESTORNO"]}. Detalhes: {e}'
        refund_utils_api_logger.error(msg)
        refund_utils_api_logger.error(traceback_of_last_n_lines())
        return ResultHandle.Fail(msg, data=refund_raw_data)


def estorno_venda_orm_without_date_formatting(csv_row: Dict[str, str]) -> EstornoLog:

    return EstornoLog(
        codigoFilialOrigem=int(csv_row.get("FILIAL_ORIGEM")),
        cnpjFilialOrigem=csv_row.get("CNPJ_FILIAL_ORIGEM"),
        codigoFilialDestino=int(csv_row.get("FILIAL_DESTINO")),
        cnpjFilialDestino=csv_row.get("CNPJ_FILIAL_DESTINO"),
        numeroCupom=int(csv_row.get("NUMERO_CUPOM")),
        numeroCoo=int(csv_row.get("NUMERO_COO")),
        nomeCliente=csv_row.get("NOME_CLIENTE"),
        pdvInformado=int(csv_row.get("PDV_DEVOLUCAO")),
        numeroPedidoVTEX=csv_row.get("NUMERO_VTEX") if csv_row.get("NUMERO_VTEX") is not None else '',
        numeroPedidoDelivery=csv_row.get("NUMERO_DELIVERY"),
        dataMovimento=csv_row.get("DATA_MOVIMENTO"),
        valorTotalVenda=float(csv_row.get("VALOR_CUPOM")) if csv_row.get("VALOR_CUPOM") else None,
        valorEstorno=float(csv_row.get("VALOR_ESTORNO")) if csv_row.get("VALOR_ESTORNO") else None,
        dataDevolucao=csv_row.get("DATA_DEVOLUCAO"),
        nsutef=csv_row.get("NSU_TEF"),
        nsuhost=csv_row.get("NSU_HOST"),
        bandeira=csv_row.get("BANDEIRA") if csv_row.get("BANDEIRA") is not None else '',
        telefone=csv_row.get("TELEFONE_CLIENTE") if csv_row.get("TELEFONE_CLIENTE") is not None else '',
        email=csv_row.get("EMAIL_CLIENTE"),
        numeroPreVenda=csv_row.get("NUMERO_PRE_VENDA"),
        planoAssinatura=csv_row.get("E_PLANO_ASSINATURA"),
        canalVendas=csv_row.get("CANAL_VENDAS"),
        cartaoSempre=csv_row.get("NUMERO_CARTAO_SEMPRE"),
        parcelas=int(csv_row.get("FLDE_QT_PAR_CAR")) if csv_row.get("FLDE_QT_PAR_CAR") else None,
        tipo=csv_row.get("FLDE_TP_POS"),
        flagPagamentoUni=csv_row.get("E_PAGAMENTO_UNIFICADO"),
        nsuPos=csv_row.get("NSU_POS"),
        idDevtrocaCab=int(csv_row.get("ID_DEVTROCA_CAB")),
        cooPagUni=csv_row.get("COO_PAGAMENTO_UNIFICADO"),
        idEstorno=int(csv_row.get("ID_ESTORNO")),
        dataVenda=csv_row.get("DATA_VENDA"),
        tid=csv_row.get("T_ID"),
        codigoAutorizacao=csv_row.get("CODIGO_AUTORIZAÇÂO"),
        nomeEmpresa=csv_row.get("NOME_EMPRESA"),
    )