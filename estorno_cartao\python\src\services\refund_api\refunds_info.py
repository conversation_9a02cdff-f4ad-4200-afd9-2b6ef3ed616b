from requests import get, Response
from requests import Response
from models.result_handle import ResultHandle
from datetime import datetime, timedelta
from services.refund_api.refunds_utils import legacy_api_orm
from Config.vars import EXTRA_FARMA_CASE
from services.logger import create_logger
from typing import List, Union
from traceback import format_exc

refund_api_logger = create_logger(__name__, without_handler = True)

BASE_URL = 'https://k8s.pmenos.com.br/apipedidosestornotrocadevolucao'
GET_REFUNDS_BY_PERIOD_AND_POS_KIND = '/api/pedidos-estorno/buscar-por-periodo-e-marcador-venda'


class RefundApi:
    def __init__(self, traceback_lines: int = 3):
        """
        Initialize RefundApi with configurable traceback lines.

        Args:
            traceback_lines (int): Number of traceback lines to print in exceptions. Default is 3.
        """
        self.traceback_lines = traceback_lines

    def get_refunds_by_period(self, initial_date: str, final_date: str, pos_kind: str, enterprise: str) -> ResultHandle:

        request_url = f'{BASE_URL}{GET_REFUNDS_BY_PERIOD_AND_POS_KIND}?data_inicial={initial_date}&data_final={final_date}&marcador_venda_pos={pos_kind}'

        return self.__treat_response(get(request_url), enterprise)
    
    def get_not_processed_refunds_by_days_back_and_pos_kind(self, days_back: int, pos_kind: str, entreprise: str) -> ResultHandle:
        """Obter os pedidos de estorno não processados nos últimos `n` dias

        Args:
            days_back (int): Dias para voltar
            pos_kind (str): tipo de venda 
            entreprise (str): de qual empresa do grupo paguemenos o estorno se originou

        Returns:
            ResultHandle: Resultado da requisição
        """

        if not isinstance(days_back, int):
            return ResultHandle.Fail(None, 'days_back deve ser um inteiro')

        initial_date = (datetime.now() - timedelta(days=days_back)).strftime('%Y-%m-%d')
        final_date = (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d')

        request_url = f'{BASE_URL}{GET_REFUNDS_BY_PERIOD_AND_POS_KIND}?data_inicial={initial_date}&data_final={final_date}&marcador_venda_pos={pos_kind}'

        return self.__treat_response(get(request_url), entreprise)
    
    def __treat_response(self, response: Response, enterprise: str) -> Union[List[ResultHandle],ResultHandle]:
        """Tratar a resposta da API fazendo o mapeamento dos resultado no
        dicionário usado para fazer o processamento de estorno.


        Args:
            response (Response): resposta da API

        Returns:
            List[ResultHandle]: Objeto contendo o resultado do tratamento, caso de sucesso, 
            contém uma lista de dicionários com as informações de estorno, se ocorrer alguma falha, 
            no mapeamento de um pedido de estorno ao invés de um dicionário, esse estorno será 
            representado por um ResultHandle de erro contendo as informações do estorno original sem tratamento

            ResultHandle: Falha na requisição, todos estorno com erro de ORM ou erro não mapeado

        """

        try:
            response_json = response.json()

            if not response.ok: return ResultHandle.Fail("Erro na requisição para verificar novos estornos", response_json)

            if enterprise == EXTRA_FARMA_CASE:
                not_processed_refunds = tuple(filter(lambda refund: int(refund['FILIAL_ORIGEM']) > 7000, response_json['data']))
            else:
                not_processed_refunds = tuple(filter(lambda refund: int(refund['FILIAL_ORIGEM']) < 7000, response_json['data']))
            
            if len(not_processed_refunds) == 0:
                refund_api_logger.info(f"Não novos estornos para a empresa {enterprise} dentro desse intervalo de tempo") 
                return ResultHandle.Ok([])

            not_processed_refunds = list(map(lambda raw_refund: legacy_api_orm(raw_refund), not_processed_refunds))

            is_all_with_orm_error = all((isinstance(refund, ResultHandle) for refund in not_processed_refunds))
            if is_all_with_orm_error: return ResultHandle.Fail("Erro na ORM de todos os novos estornos", not_processed_refunds)
            return ResultHandle.Ok(not_processed_refunds)

            
        except Exception as e:
            tb_lines = format_exc().splitlines()
            limited_traceback = '\n'.join(tb_lines[-self.traceback_lines:])

            msg = f'Erro ao tratar a resposta da API para verificar novos estornos. Error: {e}\nTraceback (last {self.traceback_lines} lines):\n{limited_traceback}'
            refund_api_logger.error(msg)
            return ResultHandle.Fail(msg, response)
    


        