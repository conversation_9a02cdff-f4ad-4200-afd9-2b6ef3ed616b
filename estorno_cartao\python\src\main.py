import utils
import sys
from executers.main_pos_extrafarma import POSEX
from executers.main_pos import POS 
from executers.main_tef import TEF
from executers.main_tef_extrafarma import TEFEX
from executers.main_retorno import RetornoRequisicao
from executers.main_atualizar_token import AtualizarToken
from services.multi_report_handler import *
from services.nupay.nupay_backward import NupayTracker
from services.itau.itau_return import ItauReturn
from services.credshop.credshop_tracker import CredshopTracker
from services.logger import create_logger
from services.zendesk.ticket_follow_up import verify_not_solved_tickets
from Config.vars import EXE_OPTIONS
from models.refund_erros import GeneralRefundError


################################
# Argumento 0: pos pmenos      #
# Argumento 1: tef pmenos      #
# Argumento 2: retorno Cielo   #
# Argumento 3: retorno Getnet  #
# Argumento 4: tef Extrafarma  #
# Argumento 5: pos Extrafarma  #
# Argumento 6: Atl.Token Cielo #
# Argumento 7: Retorno Nupay   #
# Argumento 8: Retorno Itau    #
# Argumento 9: Verificar tickets zendesk #
# Argumento 10: Retorno Credshop#
################################


main_logger = create_logger('', "logs.log")
main_logger.setLevel(20)

def main(option: int = None):

    if (option is None and len(sys.argv) > 1) or (len(sys.argv) > 1):
        option = int(sys.argv[1]) 
    elif option is None and len(sys.argv) <= 1:
        raise ValueError("Opção inválida")
    else:
        option = option

    app_config = utils.get_config()  

    try:

        if option == 0:
            
            main_ecommerce = POS(app_config)                   
            main_ecommerce.execute_process()

        elif option == 1:

            TEF(app_config).execute_process()

        elif option == 2:        

            retorno_service = RetornoRequisicao(app_config)
            retorno_service.processar_retorno_cielo()

        elif option == 3:
            retorno_service = RetornoRequisicao(app_config)
            retorno_service.processar_retorno_getnet()    

        elif option == 4:
            TEFEX(app_config).execute_process()  
        
        elif option == 5:
            POSEX(app_config).execute_process()  

        elif option == 6:
            AtualizarToken(app_config).execute()

        elif option == 7:
            nupay_tracker = NupayTracker(app_config)
            nupay_tracker.execute()
        
        elif option == 8:
            itau_return = ItauReturn(app_config)
            itau_return.verificar_estornos_em_andamento()
        
        elif option == 9:
            verify_not_solved_tickets()
        
        elif option == 10:
            credshop_tracker = CredshopTracker()
            credshop_tracker.track_credshop_refunds()

    except KeyboardInterrupt:
        main_logger.info("Processo interrompido via teclado")
    except Exception as e:
        main_logger.error(f'Erro ao executar o processo {EXE_OPTIONS.get(option)}')
        main_logger.error(utils.traceback_of_last_n_lines())
        log_report()
        raise e

if __name__ == '__main__': 
    main(2)
    
    