from src.utils import get_config
from src.services.vtex_api_service import VTEXHandler


if __name__ == '__main__':

    vtex_essencial_dict = {
                            "numeroPedidoVTEX":'155344693221001',
                            "valorEstorno": 17.16,
                            "bandeira":'Pix <PERSON>',
                            "valorTotalVenda": 17.16
                          }

    app_config = get_config()
    vtex_service = VTEXHandler(app_config['api']['vtex']) 
    dados_vtex = vtex_service.retrieve_data(vtex_essencial_dict) 


    print()