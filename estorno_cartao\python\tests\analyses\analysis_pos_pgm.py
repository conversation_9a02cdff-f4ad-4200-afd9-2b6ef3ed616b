from src.executers.main_pos import POS
from src.utils import get_config
from src.Connections.search_db import SearchDb
from src.services.refund_api.refunds_utils import legacy_api_orm



if __name__ == '__main__':

    id_estorno = 481351
    search = SearchDb()
    refund_info = legacy_api_orm(search.search_refund_by_refund_id_in_estorno_venda(id_estorno)[0][0])

    refunds = [refund_info]
    pos = POS(get_config())


    pos.execute_process(refunds)