from email import message
from signal import raise_signal
import requests
import json
#from services.credentials import Credentials

class Vindi:
    
    def __init__(self) -> None:
        pass
        #self.credentials = Credentials()

    def cancellation(self,filial:int,pre_sale:str,refund_value:float):
        url = "https://k8s.pmenos.com.br/checkouthub/api/checkout/EstornoCheckout"

        payload = json.dumps({
            "reference_id":f"{str(filial)}_{pre_sale}",
            "refund_value":refund_value,
            "transaction_id":f"{str(filial)}_{pre_sale}"
        })

        headers = {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJpZCI6Ijg4NTg3ZDRiLTY4NWQtNDk1OS1iMTc1LWNmMWYxNzJlYmMzMSIsIm5hbWUiOiJib3QtZXN0IiwiY2hhdmUiOiJlc3Rvcm5vX3BhcmNpYWxfYm90IiwicHJvdiI6IlZpbmRpIiwibmJmIjoxNzU0NTk3MjgyLCJleHAiOjE3ODYxMzMyODIsImlzcyI6Imh0dHA6Ly9rOHMucG1lbm9zLmNvbS5ici9jaGVja291dGh1YiIsImF1ZCI6Imh0dHA6Ly9rOHMucG1lbm9zLmNvbS5ici9jaGVja291dGh1YiJ9.HrYNfRGfA54yVsEuHWWgEAG7orV1ARJ3F4rhZZobh0J0Cyyw-AWF7j8ph7lXci1-eIk94OFjhqt9KxsqnPBqQw'
        }
        
        try:

            response = requests.request("POST", url, headers=headers, data=payload)
            mensagem = json.loads(response.text)

        except Exception as error:

            mensagem = "Erro na respota do pix Vindi"
            print(f"{mensagem}. Detalhes: {error}")   
            mensagem = mensagem + f". Resposta: {response.text}"

            return mensagem, -10

            
        if response.status_code == 401:
            mensagem = "Erro na respota do pix Vindi"

            return mensagem, -20

        if 'Fatura já foi cancelada' == mensagem['message']:
            mensagem = 'Fatura já foi cancelada'
            return mensagem, -3


        if 'message' not in mensagem:
            msg = "erro ao realizar cancelamento"
            return f"{msg} : {response.text}",-1

        if  mensagem['message'] == 'sucesso ao processar':
            return "SUCESS",0  

        else:
            return mensagem['message'],-2  

        