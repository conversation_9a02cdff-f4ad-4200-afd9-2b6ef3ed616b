
from datetime import datetime
import requests
import json
from pathlib import Path
import sys

def local_execution_with_others_projects_modules(current_file: str, directory_to_go_back: int = 1) -> Path:
    """
    If in a local execution the current module uses resources from another \n
    module that is in the same project i.e.(this module is in the same \n
    directory as the other module) and they aren't in the same level. \n
    So you need to add the common dictory between the modules to the Paths of execution \n
    
    Args:
        `current_file` (str): file path who calls the other modules

    Returns:
        `Path`: common directory Path
    """
    
    src_folder = Path(current_file.split(":")[0].upper() + ":" + current_file.split(":")[1])
    for _ in range(directory_to_go_back): src_folder = src_folder.parent
    if src_folder.__str__() not in sys.path: sys.path.append(str(src_folder))
    if Path.cwd().__str__() not in sys.path: sys.path.append(Path.cwd().__str__())

    return src_folder

src_folder = local_execution_with_others_projects_modules(__file__, 2)
from Connections.search_db import SearchDb
from models.card_refund_info import CardRefundInfo
from Config.vars import EXTRA_FARMA_CASE, PAGUE_MENOS_CASE
import utils
from services.logger import create_logger

cielo_logger = create_logger(__name__, without_handler = True)

DEPRECATED_CIELO_TOKEN_MSG = "Access Token in the request, identified by HEADER Authorization"

class ServiceCielo:

    def __init__(self,config:dict) -> None:
        self.search_db = SearchDb()

        self.url = config['api']['cielo']['api_base_url']
        self.consulta = config['api']['cielo']['resource_consulta']
        self.body = config['cielo_body_envio']
        self.__api_base_url = config['api']['movimento-bpm']['api_base_url']
        self.__resource_getnet = config['resource']['cielo']
        self.refresh_token_cielo_ext = config['api']['cielo']['resource_refresh_token_ext']
        self.refresh_token_cielo = config['api']['cielo']['resource_refresh_token']
        self.auth_cielo_pm = config['api']['cielo']['authorization_pm']
        self.auth_cielo_ext = config['api']['cielo']['authorization_ext']
        self.resource_refund = config['api']['cielo']['resource_refund']
        
        super().__init__()

    def estornoCielo(self, card_requesition_info: CardRefundInfo, empresa: str):
        
        url = self.url + self.resource_refund
        body = self.body

        data_convertida = datetime.strptime(card_requesition_info.sale_date,'%Y-%m-%d').strftime('%d-%m-%Y %H:%M:%S')
        
        body['transactions'][0]['nsu'] = str(card_requesition_info.nsu_tef)
        body['transactions'][0]['merchantID'] = card_requesition_info.establishment_code
        body['transactions'][0]['authorizationCode'] = card_requesition_info.auth_code
        body['transactions'][0]['refundAmount']['value'] = str(card_requesition_info.refund_value)
        body['transactions'][0]['saleAmount']['value'] = str(card_requesition_info.payment_method_value)
        body['transactions'][0]['transactionDate'] = data_convertida

        print(body)

        if empresa == PAGUE_MENOS_CASE:
            token_pmenos = self.search_db.search_token('Access_Token_Cielo')
            headers = {
                'Authorization': 'bearer '+ token_pmenos,
                'Content-Type': 'application/json'}
        else:
            token_ext = self.search_db.search_token('Access_Token_Cielo_Ext')
            headers = {
                'Authorization': 'bearer '+ token_ext,
                'Content-Type': 'application/json'}


        response = requests.request("POST", url, headers=headers, data= json.dumps(body)) 

        try:
            if DEPRECATED_CIELO_TOKEN_MSG in response.text:
                cielo_logger.warning("Token da cielo está desatualizado. pedido de estorno será reprocessado na próxima iteração")
                return 2
            dict_json = json.loads(response.text)
            protocolo = dict_json['refundID']
            cielo_logger.info(f"Pedido de estorno enviado para a cielo. Protocolo: {protocolo}")
            return protocolo
        except Exception as e:            
            if dict_json.get('error') != '':
                return -1

            if json.loads(response.text)['errorMessage']['returnMessage'] == 'Número NSU inválido.':
                return -2

            elif json.loads(response.text)['errorMessage']['returnMessage'] == 'Não foi possível autenticar o canal.':
                return -2    
                
            print(e)
            return -1
       
    
    def consultacielo(self, ec, protocolocielo,empresa):
        
        url = self.url + self.consulta.format(protocolocielo,ec)

        
        if empresa == 'Pague Menos':
            token_pmenos = self.search_db.search_token('Access_Token_Cielo')
            headers = {
                'Authorization': 'bearer '+ token_pmenos,
                'Content-Type': 'application/json'}

        else:
            
            token_cielo_ext = self.search_db.search_token('Access_Token_Cielo_Ext')
            headers = {
                'Authorization': 'bearer '+token_cielo_ext,
                'Content-Type': 'application/json'}     

        response_raw = requests.request("GET", url, headers=headers)            

        response = json.loads(response_raw.text)

        if 'error' in response:
            return 'TOKEN CIELO DESATUALIZADO', -1

        if 'transactions' in response:

            if len(response['transactions']) == 0:
                return f"Transação do protocolo {protocolocielo} não foi encontrado", -1

            if response['transactions'][0]['status']['type'] == 'done':
                return'REALIZADO', 1 

            elif response['transactions'][0]['status']['type'] == 'pending':
                return'AGUARDANDO', 0

            elif response['transactions'][0]['status']['type'] == 'failed':
                return'FALHA NA API', 2  

            elif response['transactions'][0]['status']['type'] == 'rejected':
                return response['transactions'][0]['status']['detail']['message'], response['transactions'][0]['status']['detail']['code']      

        else:
            if response['errorMessage']['errorMessage'] =='Estabelecimento inválido.':
                return response['errorMessage']['errorMessage'], -1

    def buscar_vendas_cielo(self):
        response = requests.get(self.__api_base_url.format(self.__resource_getnet))
        response_dict = response.text
        retorno_vendas = response_dict
        return json.loads(retorno_vendas) 


    def atulizar_token_cielo(self,refresh,empresa):
        

        payload = json.dumps({
                            "grant_type": "refresh_token",
                            "refresh_token": refresh
                            })
        
        if empresa == 'Pague Menos':
            url = self.url + self.refresh_token_cielo
            headers = {
                        'Content-Type': 'application/json',
                        'Authorization': self.auth_cielo_pm,
                    }
        else:
            url = self.url + self.refresh_token_cielo_ext
            headers = {
                        'Content-Type': 'application/json',
                        'Authorization': self.auth_cielo_ext,
                    }

        response = requests.request("POST", url, headers=headers, data=payload)
        print(response.text)

        response = json.loads(response.text)
        

        return response['access_token'], response['refresh_token']


    
    def atulizar_token_cielo_ext(self,refresh): 

        url = self.url + self.refresh_token_cielo_ext

        payload = json.dumps({
                            "grant_type": "refresh_token",
                            "refresh_token": refresh
                            })  

        headers = {
                        'Content-Type': 'application/json',
                        'Authorization': self.auth_cielo_ext,  

                    }       

        response = requests.request("POST", url, headers=headers, data=payload)
        print(response.text)

        response = json.loads(response.text)

        return response['access_token'], response['refresh_token']

    def search_refunds(self, sale_cart_info: dict):

        empresa = sale_cart_info['empresa']
        url = self.url + self.resource_refund + f'?cancelStartDate={sale_cart_info["cancelStartDate"]}' \
        f'&cancelEndDate={sale_cart_info["cancelEndDate"]}' \
        f'&rows={sale_cart_info["rows"]}' \
        f'&page={sale_cart_info["page"]}' \
        f'&merchantId={sale_cart_info["merchantId"]}' \
        f'&authorizationCode={sale_cart_info["authorizationCode"]}'

        
        if empresa == 'Pague Menos':
            token_pmenos = self.search_db.search_token('Access_Token_Cielo')
            headers = {
                'Authorization': 'bearer '+ token_pmenos,
                'Content-Type': 'application/json'}

        else:
            
            token_cielo_ext = self.search_db.search_token('Access_Token_Cielo_Ext')
            headers = {
                'Authorization': 'bearer '+token_cielo_ext,
                'Content-Type': 'application/json'}   
            
        
        response = requests.request("GET", url, headers=headers)
        print(response.text)

        response = json.loads(response.text)
        return response



if __name__ == '__main__':

    sale_cart_info = {
        "cancelStartDate": "17-04-2025",
        "cancelEndDate": "28-04-2025",
        "rows": 25,
        "page": 1,
        "merchantId": "1112093050",
        "authorizationCode": "005651",
        "empresa": "Pague Menos"
    }

    app_config = utils.get_config()  
    cielo = ServiceCielo(app_config)
    data = cielo.search_refunds(sale_cart_info)
    print("fim")