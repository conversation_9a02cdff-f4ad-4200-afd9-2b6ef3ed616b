import pytest
from src.services.refund_api.refunds_info import Refund<PERSON>pi
from src.Config.vars import PAGUE_MENOS_CASE, EXTRA_FARMA_CASE
from datetime import datetime, timedelta


@pytest.mark.parametrize("pos_kind, enterprise", [
    ('S', PAGUE_MENOS_CASE),
    ('S', EXTRA_FARMA_CASE),
    ('N', PAGUE_MENOS_CASE),
    ('N', EXTRA_FARMA_CASE),
])
class TestRefundApiIntegration():

    def test_get_not_processed_refunds_by_days_back_and_pos_kind(self, pos_kind, enterprise):
        """Testar obtenção de estornos não processados

        Resultado esperado: 
            - Retornar sucesso
        """

        result = RefundApi().get_not_processed_refunds_by_days_back_and_pos_kind(90, pos_kind, enterprise)

        assert result.success, f"Erro ao obter estornos não processados. POS_KIND: {pos_kind} - ENTERPRISE: {enterprise}"

    def test_get_not_processed_refunds_by_days_back_and_pos_kind_error(self, pos_kind, enterprise):
        """Testar obtenção de estornos não processados com erro

        Resultado esperado: 
            - Retornar erro
        """

        result = RefundApi().get_not_processed_refunds_by_days_back_and_pos_kind('90', pos_kind, enterprise)

        assert result.failure, f"Foi possível obter estornos não processados. POS_KIND: {pos_kind} - ENTERPRISE: {enterprise}"

    def test_get_period_without_refunds(self, pos_kind, enterprise):
        """Testar integração da API quando não há estornos no período

        Resultado esperado: 
            - Retornar sucesso
        """
        initial_date = (datetime.now() + timedelta(days=3)).strftime('%Y-%m-%d')
        final_date = (datetime.now() + timedelta(days=5)).strftime('%Y-%m-%d')

        result = RefundApi().get_refunds_by_period(initial_date, final_date, pos_kind, enterprise)

        assert result.success, f"Erro ao obter estornos por período"
        assert result.data == [], f"Não deveria ter encontrado estornos no período"




if __name__ == '__main__':

    #test_get_period_without_refunds()
    pytest.main([__file__])

