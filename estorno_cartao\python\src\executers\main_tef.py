
from datetime import datetime, timedelta
from services.getnet_service import ServiceGetnet
from services.itau.itau_service import ItauService
from services.multi_report_handler import *
from services.lio_service import LioService
from services.scope_api_service import ScopeApiService
from services.movimento_service import MovimentoService
from services.estorno_service import EstornoService
from services.lio_service import LioService
from services.mercado_pago_services import ServiceMercadoPago
from services.sms import SMS 
from services.cielo_service import ServiceCielo
from services.credshop.credshop_main import CredshopService
from Connections.search_db import SearchDb
import utils
from services.multi_payment_method_sale.multi_payment_method_service import get_card_refund_info_tef
from aux_temp import register_refund_activity
from services.logger import create_logger
import Config.vars as vars
from typing import Dict, List
from services.refund_api.refunds_info import RefundApi

pg_tef_logger = create_logger(__name__, without_handler = True)

class TEF():
    
    def __init__(self, config):
   
        self.config = config
        self.estorno_service = EstornoService(config)
        self.movimento_service = MovimentoService(config)
        self.getnet_services = ServiceGetnet(config)
        self.mercadopago_services = ServiceMercadoPago(config)
        self.lio_sevices = LioService(config)
        self.itau_service = ItauService(config)
        self.sms_sevices = SMS(config)
        self.service_cielo = ServiceCielo(config)
        self.search = SearchDb()
        self.ids_estorno_to_skip = []

        super().__init__()
    
    def execute_process(self, requested_refund: List[Dict] = None):

        scope_api_service = ScopeApiService(self.config)
                
        log_report("#####################")
        log_report("Iniciando Estorno TEF")
        log_report("#####################")

        if requested_refund is None:
            new_refunds = RefundApi().get_not_processed_refunds_by_days_back_and_pos_kind(15, vars.NON_POS_CASE, vars.PAGUE_MENOS_CASE)
            if new_refunds.failure: raise SystemExit(new_refunds.error_description)  
            movimento = new_refunds.data
        else:
            movimento = requested_refund

        counter = 0
        for venda in movimento:
            
            if not isinstance(venda, dict): 
                msg = f"ATENÇÃO: pedido de estorno id_estorno_fin: {venda.data['ID_ESTORNO']} com erro na ORM"
                pg_tef_logger.warning(msg)
                log_report(msg)
                continue

            counter = counter + 1

            filial_origem = venda['codigoFilialOrigem']
            cnpj_filial_origem = venda['cnpjFilialOrigem']    
            filial_destino = venda['codigoFilialDestino']
            cnpj_filial_destino = venda['cnpjFilialDestino']
            numero_cupom = venda['numeroCupom']
            numero_coo = venda['numeroCoo']
            pdv = venda['pdvDevolucao']
            nome_cliente = venda['nomeCliente']
            numero_pedido_vtex = venda['numeroPedidoVTEX']
            numero_pedido_delivery = venda['numeroPedidoDelivery']
            data_venda_movimento = venda['dataMovimento']
            valor_venda_movimento = float(venda['valorVenda'])
            valor_estorno = float(venda['valorEstorno'])
            data_devolucao = venda['dataDevolucao'] if venda['dataDevolucao'] != '0001-01-01T00:00:00' else None
            
            nsu_movimento_tef = venda['codigoNSUTEF']
            nsu_movimento_host = venda['codigoNSUHOST']

            bandeira_cartao_movimento = venda['bandeira']
            telefone = venda['telefoneCliente']
            email = venda['emailCliente']
            numero_pre_venda = venda['numeroPreVenda']
            plano_assinatura = venda['planoAssinatura']
            canal_venda = venda['canalVendas']
            cartao_sempre = venda['cartaoSempre']
            parcelas = venda['quantParcelas']
            tipo_venda = venda['tipo']
            flag_pag_uni = venda['flagPagamentoUnificado']
            nsu_pos = venda['nsuPos']
            id_troca_cab = str(venda['idTrocaCab'])
            coo_pag_uni = venda['cooPagUni']
            id_estorno = venda['idEstorno']

            new_data = data_venda_movimento.split('/')
            data_movimento_format = '{}-{}-{}'.format(new_data[2],new_data[1],new_data[0])

            nome_empresa = "Pague Menos"

            pg_tef_logger.info(f"Pedido de estorno {counter} id_estorno: {id_estorno}")

            if utils.avoid_legacy_api_duplicates(id_estorno): 
                continue

            obj_estorno_log = {
                "codigoFilialOrigem": filial_origem,
                "cnpjFilialOrigem": cnpj_filial_origem,
                "codigoFilialDestino": filial_destino,
                "cnpjFilialDestino": cnpj_filial_destino,
                "numeroCupom": numero_cupom,
                "numeroCoo": numero_coo,
                "nomeCliente": nome_cliente,
                "pdvInformado": pdv,
                "numeroPedidoVTEX": numero_pedido_vtex,
                "numeroPedidoDelivery": numero_pedido_delivery,
                "dataMovimento": '{}-{}-{}'.format(new_data[2],new_data[1],new_data[0]),
                "valorTotalVenda": valor_venda_movimento,
                "valorEstorno": valor_estorno,
                "dataDevolucao":data_devolucao,
                "nsutef": nsu_movimento_tef,
                "nsuhost": nsu_movimento_host,
                "bandeira": bandeira_cartao_movimento,
                "telefone": telefone,
                "email": email,
                "numeroPreVenda": numero_pre_venda,
                "planoAssinatura": plano_assinatura,
                "canalVendas": canal_venda,                
                "cartaoSempre": cartao_sempre,
                "parcelas": parcelas,
                "tipo": tipo_venda,
                "flagPagamentoUni": flag_pag_uni,
                "nsuPos": nsu_pos,
                "idDevtrocaCab": id_troca_cab,
                "cooPagUni": coo_pag_uni,
                "idEstorno": id_estorno,
                "lote":self.__montar_lote('{}-{}-{}'.format(new_data[2],new_data[1],new_data[0])),                              
                "tipoEstorno":"Estorno PDV",
                "nomeEmpresa":nome_empresa
            }
        

            tipo_pagamento = utils.is_a_mapped_payment_method(bandeira_cartao_movimento, 
                                                                self.estorno_service, 
                                                                obj_estorno_log)
            
            if tipo_pagamento == -1: continue

            if tipo_pagamento == 'pix':
                
                if 'itau' in obj_estorno_log['bandeira'].lower():
                    
                    obj_estorno_log = self.itau_service.estornoitau(id_estorno, nome_empresa, obj_estorno_log)
                    register_refund_activity(obj_estorno_log, self.estorno_service)
                    continue
                
                obj_estorno_log['bandeira'] = 'Pix'

                tipo = 'tef_pgm'
                estorno = self.mercadopago_services.estornopix(nsu_movimento_host,valor_venda_movimento,valor_estorno,tipo)
                if estorno == -1:

                    obj_estorno_log['motivo'] = 'codigo nsu invalido no Mercado Pago' 
                    obj_estorno_log['status'] = vars.STATUS_PENDENCY_FOR_CSC
                    self.estorno_service.record_refund_processment(obj_estorno_log)
                    log_report("estorno {}: codigo nsu invalido no Mercado Pago".format(id_estorno))
                    continue
                    
                elif estorno == -2:
                    
                    obj_estorno_log['motivo'] = 'Estorno ja consta como REALIZADO' 
                    obj_estorno_log['status'] = vars.STATUS_REFUND_PROCESS_END 
                    self.estorno_service.record_refund_processment(obj_estorno_log)
                    log_report("estorno {}: Estorno ja consta como REALIZADO".format(id_estorno))
                    continue

                elif estorno == -20:
                    error_description = "venda desse estorno consta como rejeitada"
                    msg = f"{vars.BASE_MSG.format(id_estorno=id_estorno)}{error_description}"
                    obj_estorno_log['motivo'] = error_description
                    obj_estorno_log['status'] = vars.STATUS_REFUND_PROCESS_END 
                    self.estorno_service.record_refund_processment(obj_estorno_log)
                    log_report(msg)
                    pg_tef_logger.info(msg)
                    continue
                    
                elif estorno == -3:
                    
                    obj_estorno_log['motivo'] = 'Estorno ja consta como Parcialmete REALIZADO' 
                    obj_estorno_log['status'] = vars.STATUS_REFUND_PROCESS_END
                    self.estorno_service.record_refund_processment(obj_estorno_log)
                    log_report("estorno {}:Estorno ja consta como Parcialmete REALIZADO".format(id_estorno))
                    
                    continue
                    
                elif estorno == -30:
                    error_description = "status de pix mercado pago não mapeada"
                    msg = f"{vars.BASE_MSG.format(id_estorno=id_estorno)}{error_description}"
                    obj_estorno_log['motivo'] = error_description
                    obj_estorno_log['status'] = vars.STATUS_PENDENCY_FOR_CSC
                    self.estorno_service.record_refund_processment(obj_estorno_log)
                    log_report(msg)
                    pg_tef_logger.info(msg)
                    continue

                    
                elif estorno == -4:
                    
                    obj_estorno_log['motivo'] = 'Valor do estonar Maior que o valor total da Venda' 
                    obj_estorno_log['status'] = vars.STATUS_PENDENCY_FOR_CSC
                    self.estorno_service.record_refund_processment(obj_estorno_log)
                    log_report("estorno {}:Valor do estonar Maior que o valor total da Venda".format(id_estorno))
                    
                    continue

                elif estorno == -5:
                    obj_estorno_log['motivo'] = 'REALIZADO'
                    obj_estorno_log['status'] = vars.STATUS_REFUND_PROCESS_END
                    self.estorno_service.record_refund_processment(obj_estorno_log)
                    self.sms_sevices.sendsms(telefone,cartao_sempre,valor_estorno)
                    log_report("estorno {}:REALIZADO".format(id_estorno))
                    
                    continue 
                
                elif estorno == -6:
                    obj_estorno_log['motivo'] = 'Dados inconsistentes para solicitação de estorno no Mercado pago'  
                    obj_estorno_log['status'] = vars.STATUS_PENDENCY_FOR_CSC 
                    self.estorno_service.record_refund_processment(obj_estorno_log)
                    log_report("Estorno Extrafarma{}: Dados inconsistentes para estorno no Mercado pago".format(id_estorno))
                    continue

                elif estorno == -11:
                    obj_estorno_log['motivo'] = 'Venda Pix passou do prazo de 60 dias.'
                    obj_estorno_log['status'] = vars.STATUS_PENDENCY_FOR_CSC
                    self.estorno_service.record_refund_processment(obj_estorno_log)                   
                    log_report("Estorno {}:Venda Pix passou do prazo de 60 dias.".format(id_estorno))
                    continue  
                
                else:
                    error_description = "resposta de procesamento de estorno pix mercado pago não mapeada"
                    msg = f"{vars.BASE_MSG.format(id_estorno=id_estorno)}{error_description}"
                    obj_estorno_log['motivo'] = error_description
                    obj_estorno_log['status'] = vars.STATUS_PENDENCY_FOR_CSC
                    self.estorno_service.record_refund_processment(obj_estorno_log)                   
                    log_report(msg)
                    pg_tef_logger.info(msg)
                    continue
           
            
            elif tipo_pagamento == 'cartao':
                
                sale_date_obj = datetime.strptime(obj_estorno_log["dataMovimento"], '%Y-%m-%d')
                skip_a_day = timedelta(days=1, hours=7) 

                if (sale_date_obj + skip_a_day) >= datetime.today():
                    pg_tef_logger.info(f'Venda do pedido de id_estorno: {obj_estorno_log["idEstorno"]} ainda não subiu')       #245474      
                    continue
                
                dados_scope = scope_api_service.find_sale(data_movimento_format, 
                                                          filial_origem, 
                                                          nsu_movimento_tef, 
                                                          nome_empresa,
                                                          obj_estorno_log) 
                if dados_scope == -1:
                    print('...Dados de estorno sem correspondente em VDA ou Scope...')
                    obj_estorno_log['motivo'] = 'Dados de estorno sem correspondente em VDA ou Scope' 
                    obj_estorno_log['status'] = vars.STATUS_PENDENCY_FOR_CSC   
                    self.estorno_service.record_refund_processment(obj_estorno_log) 
                    log_report("estorno {}: Dados de estorno sem correspondente em VDA ou Scope".format(id_estorno))
                    #self.tracking_services.post_tracking(venda, 'ANALISE', 'Em analise', content=obj_estorno_det['motivo'])                   
                    continue
                    
                
                elif dados_scope == -2:
                    print('...Venda cancelada...')
                    obj_estorno_log['motivo'] = 'Venda cancelada'
                    obj_estorno_log['status'] = vars.STATUS_REFUND_PROCESS_END
                    self.estorno_service.record_refund_processment(obj_estorno_log)
                    log_report("estorno {}:Venda cancelada".format(id_estorno))    
                    #self.tracking_services.post_tracking(venda, vars.STATUS_REFUND_PROCESS_END, vars.STATUS_REFUND_PROCESS_END, content=obj_estorno_det['motivo'])           
                    continue
                    
                elif dados_scope == -3:
                    print('...Bandeira desconhecida...')
                    obj_estorno_log['motivo'] = 'Bandeira ou adquirente desconhecida'
                    obj_estorno_log['status'] = vars.STATUS_PENDENCY_FOR_CSC    
                    self.estorno_service.record_refund_processment(obj_estorno_log)  
                    log_report("estorno {}:Bandeira ou adquirente desconhecida".format(id_estorno))             
                    #self.tracking_services.post_tracking(venda, 'ANALISE', 'Em analise', content=obj_estorno_det['motivo'])
                    continue
                    

                elif dados_scope == -5:
                    print('...Venda consta como desfeito...')
                    obj_estorno_log['motivo'] = 'Venda desfeita'
                    obj_estorno_log['status'] = vars.STATUS_PENDENCY_FOR_CSC    
                    self.estorno_service.record_refund_processment(obj_estorno_log)
                    log_report("estorno {}:Venda desfeita".format(id_estorno))               
                    #self.tracking_services.post_tracking(venda, 'ANALISE', 'Em analise', content=obj_estorno_det['motivo'])         
                    continue
                    

                elif dados_scope == -6:
                    print('...Venda consta como nao efetuada...')
                    obj_estorno_log['motivo'] = 'Venda nao efetuada'
                    obj_estorno_log['status'] = vars.STATUS_REFUND_PROCESS_END    
                    self.estorno_service.record_refund_processment(obj_estorno_log)
                    log_report("estorno {}:Venda nao efetuada".format(id_estorno))               
                    #self.tracking_services.post_tracking(venda, 'ANALISE', 'Em analise', content=obj_estorno_det['motivo'])         
                    continue
                    

                elif dados_scope == -7:
                    print('...Venda consta como cancelamento...')
                    obj_estorno_log['motivo'] = 'Venda cancelamento'
                    obj_estorno_log['status'] = vars.STATUS_REFUND_PROCESS_END    
                    self.estorno_service.record_refund_processment(obj_estorno_log)
                    log_report("estorno {}:Venda cancelamento".format(id_estorno))     
                    #self.tracking_services.post_tracking(venda, vars.STATUS_REFUND_PROCESS_END, vars.STATUS_REFUND_PROCESS_END, content=obj_estorno_det['motivo']) 
                    continue

                elif dados_scope == -4:
                    print('...Situacao nao mapeada...')
                    obj_estorno_log['motivo'] = 'Situacao de venda nao mapeada'
                    obj_estorno_log['status'] = vars.STATUS_PENDENCY_FOR_CSC    
                    self.estorno_service.record_refund_processment(obj_estorno_log)
                    log_report("estoro=no {}:Situacao de venda nao mapeada".format(id_estorno))               
                    #self.tracking_services.post_tracking(venda, 'ANALISE', 'Em analise', content=obj_estorno_det['motivo'])         
                    continue

                elif dados_scope == -8:
                    print('...Divergencia no valor da transacao...')
                    obj_estorno_log['motivo'] = 'Divergencia de valores entre Troca devolução e outras fontes (VTEX ou VDA)'
                    obj_estorno_log['status'] = vars.STATUS_PENDENCY_FOR_CSC    
                    self.estorno_service.record_refund_processment(obj_estorno_log)
                    log_report("estorno {}:Divergencia de valores".format(id_estorno))               
                    #self.tracking_services.post_tracking(venda, 'ANALISE', 'Em analise', content=obj_estorno_det['motivo'])         
                    continue

                
                print(f'dados_scope: {dados_scope}')
                obj_estorno_log['dataVenda'] = '{}-{}-{}'.format(new_data[2],new_data[1],new_data[0])
                obj_estorno_log['n_cartao'] = dados_scope['numero_cartao']
                obj_estorno_log['sequencial_controle'] = dados_scope['seq_controle']
                obj_estorno_log['tid'] = None
                obj_estorno_log['sla_id'] = None
                obj_estorno_log['codigoAutorizacao'] = dados_scope['codigo_autorizacao']


                try: 

                    obj_estorno_log = utils.verify_diff_values(obj_estorno_log, dados_scope["valor_compra"])

                except utils.DiffValueError:
                    obj_estorno_log['motivo'] = 'Divergencia de valores entre Troca devolução e outras fontes (VTEX ou VDA)'
                    obj_estorno_log['status'] = vars.STATUS_PENDENCY_FOR_CSC    
                    self.estorno_service.record_refund_processment(obj_estorno_log)
                    log_report("estorno {}:Divergencia de valores entre Troca devolução e outras fontes (VTEX ou VDA)".format(id_estorno)) 
                    continue

                
                card_requesition_info, refund_info = get_card_refund_info_tef(obj_estorno_log)
                if isinstance(card_requesition_info, vars.ResultHandle):
                    register_refund_activity(refund_info, self.estorno_service) 
                    continue
                    

            else:
                obj_estorno_log['motivo'] = 'Erro não mapeado no RPA'
                obj_estorno_log['status'] = vars.STATUS_PENDENCY_FOR_CSC
                self.estorno_service.record_refund_processment(obj_estorno_log)
                log_report("URGENTE: Erro não mapeado no RPA") 
                continue

            if utils.is_there_a_partial_refund_to_wait(obj_estorno_log): continue
                        

            if dados_scope['adquirente'] == vars.CREDSHOP_ACQUIRE_NAME.lower(): 

                credshop = CredshopService(env="prd")
                _, refund_info = credshop.process_credshop_refund(card_requesition_info, refund_info)
                self.estorno_service.record_refund_processment(refund_info)
                continue
                   

            if dados_scope['adquirente'] == vars.GETNET_ACQUIRE_NAME.lower():

                protocolo = self.getnet_services.cancel_getnet_service(card_requesition_info, vars.PAGUE_MENOS_CASE)

                if protocolo == -1: 
                    msg = "Erro ao pedir estorno na adquirente getnet"
                    obj_estorno_log['motivo'] = msg
                    obj_estorno_log['status'] = vars.STATUS_PENDENCY_FOR_CSC 
                    obj_estorno_log['adquirente'] = vars.GETNET_ACQUIRE_NAME                                           
                    self.estorno_service.record_refund_processment(obj_estorno_log)
                    log_report(f"Estorno {id_estorno}: {msg}")
                    continue
                
                obj_estorno_log['protocoloGetnet'] = protocolo
                obj_estorno_log['status'] = vars.STATUS_WAIT_GETNET 
                obj_estorno_log['adquirente'] = vars.GETNET_ACQUIRE_NAME
                obj_estorno_log['id_estorno_adquirente'] = protocolo
                self.estorno_service.record_refund_processment(obj_estorno_log)
                log_report("estorno {}:EM ANDAMENTO GETNET".format(obj_estorno_log['idEstorno']))
                continue
                

            elif dados_scope['adquirente'] == vars.CIELO_ACQUIRE_NAME.lower():  
                
                protocolo = self.service_cielo.estornoCielo(card_requesition_info, vars.PAGUE_MENOS_CASE)
                
                if protocolo == -1: 
                    msg = "Erro ao pedir estorno na adquirente cielo"
                    obj_estorno_log['motivo'] = msg
                    obj_estorno_log['status'] = vars.STATUS_PENDENCY_FOR_CSC
                    obj_estorno_log['adquirente'] = vars.CIELO_ACQUIRE_NAME    
                    self.estorno_service.record_refund_processment(obj_estorno_log)
                    log_report("Estorno {}: Erro ao pedir estorno na adquirente cielo".format(id_estorno))
                    continue

                elif protocolo == 2:
                    continue
                
                elif protocolo == -2:
                    print('...Codigo NSU não localizado na Cielo...')
                    obj_estorno_log['motivo'] = 'Codigo NSU não localizado na Cielo'
                    obj_estorno_log['status'] = vars.STATUS_PENDENCY_FOR_CSC
                    obj_estorno_log['adquirente'] = vars.CIELO_ACQUIRE_NAME        
                    self.estorno_service.record_refund_processment(obj_estorno_log)
                    log_report("estorno {}:Codigo NSU não localizado na Cielo".format(id_estorno))
                    continue
                    

                obj_estorno_log['protocoloCielo'] = protocolo
                obj_estorno_log['status'] = vars.STATUS_WAIT_CIELO
                obj_estorno_log['adquirente'] = vars.CIELO_ACQUIRE_NAME 
                obj_estorno_log['id_estorno_adquirente'] = protocolo   
                print(obj_estorno_log)
                self.estorno_service.record_refund_processment(obj_estorno_log)
                log_report("estorno {}:{}".format(id_estorno, vars.STATUS_WAIT_CIELO))
        
        log_report("Fila TEF Paguemenos finalizada")   
            
        return 0


    def __montar_lote(self, data:str) -> str:
        
        if '-' in data:
            _data_aux = data.split('-')
            lote = datetime.strftime(datetime.strptime('{}/{}/{}'.format(_data_aux[2], _data_aux[1], _data_aux[0]), 
                                                        '%d/%m/%Y'), '%y%m%d')
        else: lote = datetime.strftime(datetime.strptime(data, '%d/%m/%Y'), '%y%m%d')

        return lote             



                    