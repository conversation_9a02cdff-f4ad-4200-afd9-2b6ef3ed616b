from typing import Optional, Dict
from dataclasses import dataclass
from Config.vars import ResultHandle
from datetime import datetime

__all__ = ["EstornoLog", 
           "turn_estorno_log_to_dataclasse", 
           "estorno_venda_log_orm"]

@dataclass
class EstornoLog:
    codigoFilialOrigem: Optional[str] = None
    cnpjFilialOrigem: Optional[str] = None
    codigoFilialDestino: Optional[str] = None
    cnpjFilialDestino: Optional[str] = None
    numeroCupom: Optional[str] = None
    numeroCoo: Optional[str] = None
    nomeCliente: Optional[str] = None
    pdvInformado: Optional[str] = None
    numeroPedidoVTEX: Optional[str] = None
    numeroPedidoDelivery: Optional[str] = None
    dataMovimento: Optional[str] = None
    valorTotalVenda: Optional[float] = None
    valorEstorno: Optional[float] = None
    dataDevolucao: Optional[str] = None
    nsutef: Optional[str] = None
    nsuhost: Optional[str] = None
    bandeira: Optional[str] = None
    telefone: Optional[str] = None
    email: Optional[str] = None
    numeroPreVenda: Optional[str] = None
    planoAssinatura: Optional[str] = None
    canalVendas: Optional[str] = None
    cartaoSempre: Optional[str] = None
    parcelas: Optional[int] = None
    tipo: Optional[str] = None
    flagPagamentoUni: Optional[str] = None
    nsuPos: Optional[str] = None
    idDevtrocaCab: Optional[str] = None
    cooPagUni: Optional[str] = None
    idEstorno: Optional[str] = None
    lote: Optional[str] = None
    tipoEstorno: Optional[str] = None
    nomeEmpresa: Optional[str] = None
    flagrepro: Optional[str] = None
    instancia: Optional[str] = None
    dataVenda: Optional[str] = None
    n_cartao: Optional[str] = None
    sequencial_controle: Optional[str] = None
    tid: Optional[str] = None
    sla_id: Optional[str] = None
    codigoAutorizacao: Optional[str] = None
    finalizador: Optional[str] = None
    motivo: Optional[str] = None
    status: Optional[str] = None
    id_pix: Optional[str] = None
    idEstornoCab: Optional[str] = None
    protocoloGetnet: Optional[str] = None
    protocoloCielo: Optional[str] = None
    valor_do_finalizador: Optional[str] = None
    id_estorno_cab: Optional[str] = None
    detalhamento: Optional[str] = None
    adquirente: Optional[str] = None
    id_estorno_adquirente: Optional[str] = None
    data_resp_adquirente: Optional[str] = None



def turn_estorno_log_to_dataclasse(data: Dict) -> EstornoLog:
    return EstornoLog(
        codigoFilialOrigem=data.get("codigoFilialOrigem", None),
        cnpjFilialOrigem=data.get("cnpjFilialOrigem", None),
        codigoFilialDestino=data.get("codigoFilialDestino", None),
        cnpjFilialDestino=data.get("cnpjFilialDestino", None),
        numeroCupom=data.get("numeroCupom", None),
        numeroCoo=data.get("numeroCoo", None),
        nomeCliente=data.get("nomeCliente", None),
        pdvInformado=data.get("pdvInformado", None),
        numeroPedidoVTEX=data.get("numeroPedidoVTEX", None),
        numeroPedidoDelivery=data.get("numeroPedidoDelivery", None),
        dataMovimento=data.get("dataMovimento", None),
        valorTotalVenda=data.get("valorTotalVenda", None),
        valorEstorno=data.get("valorEstorno", None),
        dataDevolucao=data.get("dataDevolucao", None),
        nsutef=data.get("nsutef", None),
        nsuhost=data.get("nsuhost", None),
        bandeira=data.get("bandeira", None),
        telefone=data.get("telefone", None),
        email=data.get("email", None),
        numeroPreVenda=data.get("numeroPreVenda", None),
        planoAssinatura=data.get("planoAssinatura", None),
        canalVendas=data.get("canalVendas", None),
        cartaoSempre=data.get("cartaoSempre", None),
        parcelas=data.get("parcelas", None),
        tipo=data.get("tipo", None),
        flagPagamentoUni=data.get("flagPagamentoUni", None),
        nsuPos=data.get("nsuPos", None),
        idDevtrocaCab=data.get("idDevtrocaCab", None),
        cooPagUni=data.get("cooPagUni", None),
        idEstorno=data.get("idEstorno", None),
        lote=data.get("lote", None),
        tipoEstorno=data.get("tipoEstorno", None),
        nomeEmpresa=data.get("nomeEmpresa", None),
        flagrepro=data.get("flagrepro", None),
        instancia=data.get("instancia", None),
        dataVenda=data.get("dataVenda", None),
        n_cartao=data.get("n_cartao", None),
        sequencial_controle=data.get("sequencial_controle", None),
        tid=data.get("tid", None),
        sla_id=data.get("sla_id", None),
        codigoAutorizacao=data.get("codigoAutorizacao", None),
        finalizador=data.get("finalizador", None),
        motivo=data.get("motivo", None),
        status=data.get("status", None),
        id_pix = data.get("id_pix", None),
        idEstornoCab = data.get("nomeArquivo", None), #protocoloGetnet
        protocoloGetnet = data.get("protocoloGetnet", None),  #protocoloCielo
        protocoloCielo = data.get("protocoloCielo", None),
        valor_do_finalizador = data.get("valor_do_finalizador", None),
        detalhamento = data.get("detalhamento", None),
        adquirente = data.get("adquirente", None),
        id_estorno_adquirente = data.get("id_estorno_adquirente", None),
        data_resp_adquirente = data.get("data_resp_adquirente", None)
    )



def estorno_venda_log_orm(csv_row: Dict[str, str]) -> EstornoLog:

    return EstornoLog(
        codigoFilialOrigem=csv_row.get("FILIAL_ORIGEM"),
        cnpjFilialOrigem=csv_row.get("CNPJ_FILIAL_ORIGEM"),
        codigoFilialDestino=csv_row.get("FILIAL_DESTINO"),
        cnpjFilialDestino=csv_row.get("CNPJ_FILIAL_DESTINO"),
        numeroCupom=csv_row.get("NUMERO_CUPOM"),
        numeroCoo=csv_row.get("NUMERO_COO"),
        nomeCliente=csv_row.get("NOME_CLIENTE"),
        pdvInformado=csv_row.get("PDV_DEVOLUCAO"),
        numeroPedidoVTEX=csv_row.get("NUMERO_VTEX"),
        numeroPedidoDelivery=csv_row.get("NUMERO_DELIVERY"),
        dataMovimento=format_date(csv_row.get("DATA_MOVIMENTO")),
        valorTotalVenda=float(csv_row.get("VALOR_CUPOM")) if csv_row.get("VALOR_CUPOM") else None,
        valorEstorno=float(csv_row.get("VALOR_ESTORNO")) if csv_row.get("VALOR_ESTORNO") else None,
        dataDevolucao=format_date(csv_row.get("DATA_DEVOLUCAO")),
        nsutef=csv_row.get("NSU_TEF"),
        nsuhost=csv_row.get("NSU_HOST"),
        bandeira=csv_row.get("BANDEIRA"),
        telefone=csv_row.get("TELEFONE_CLEINTE"),
        email=csv_row.get("EMAIL_CLEINTE"),
        numeroPreVenda=csv_row.get("NUMERO_PRE_VENDA"),
        planoAssinatura=csv_row.get("E_PLANO_ASSINATURA"),
        canalVendas=csv_row.get("CANAL_VENDAS"),
        cartaoSempre=csv_row.get("NUMERO_CARTAO_SEMPRE"),
        parcelas=int(csv_row.get("FLDE_QT_PAR_CAR")) if csv_row.get("FLDE_QT_PAR_CAR") else None,
        tipo=csv_row.get("FLDE_TP_POS"),
        flagPagamentoUni=csv_row.get("E_PAGAMENTO_UNIFICADO"),
        nsuPos=csv_row.get("NSU_POS"),
        idDevtrocaCab=csv_row.get("ID_DEVTROCA_CAB"),
        cooPagUni=csv_row.get("COO_PAGAMENTO_UNIFICADO"),
        idEstorno=csv_row.get("ID_ESTORNO"),
        dataVenda=format_date(csv_row.get("DATA_VENDA")),
        tid=csv_row.get("T_ID"),
        codigoAutorizacao=csv_row.get("CODIGO_AUTORIZAÇÂO"),
        nomeEmpresa=csv_row.get("NOME_EMPRESA"),
        instancia=csv_row.get("ID_INST_BPM"),
        motivo=csv_row.get("MOTIVO"),
        status=csv_row.get("STATUS"),
        detalhamento=csv_row.get("DETALHAMENTO"),
        protocoloGetnet=csv_row.get("PROTOCOLO_GETNET"),
        protocoloCielo=csv_row.get("PROTOCOLO_CIELO"),
        id_estorno_cab=csv_row.get("ID_ESTORNO_CAB"),
        adquirente=csv_row.get("ADQUIRENTE"),
        id_estorno_adquirente=csv_row.get("ID_ESTORNO_ADQUIRENTE"),
        data_resp_adquirente=csv_row.get("DATA_RESP_ADQUIRENTE")
    )



def estorno_venda_orm(csv_row: Dict[str, str]) -> EstornoLog:

    return EstornoLog(
        codigoFilialOrigem=csv_row.get("FILIAL_ORIGEM"),
        cnpjFilialOrigem=csv_row.get("CNPJ_FILIAL_ORIGEM"),
        codigoFilialDestino=csv_row.get("FILIAL_DESTINO"),
        cnpjFilialDestino=csv_row.get("CNPJ_FILIAL_DESTINO"),
        numeroCupom=csv_row.get("NUMERO_CUPOM"),
        numeroCoo=csv_row.get("NUMERO_COO"),
        nomeCliente=csv_row.get("NOME_CLIENTE"),
        pdvInformado=csv_row.get("PDV_DEVOLUCAO"),
        numeroPedidoVTEX=csv_row.get("NUMERO_VTEX"),
        numeroPedidoDelivery=csv_row.get("NUMERO_DELIVERY"),
        dataMovimento=format_date(csv_row.get("DATA_MOVIMENTO")),
        valorTotalVenda=float(csv_row.get("VALOR_CUPOM")) if csv_row.get("VALOR_CUPOM") else None,
        valorEstorno=float(csv_row.get("VALOR_ESTORNO")) if csv_row.get("VALOR_ESTORNO") else None,
        dataDevolucao=format_date(csv_row.get("DATA_DEVOLUCAO")),
        nsutef=csv_row.get("NSU_TEF"),
        nsuhost=csv_row.get("NSU_HOST"),
        bandeira=csv_row.get("BANDEIRA"),
        telefone=csv_row.get("TELEFONE_CLEINTE"),
        email=csv_row.get("EMAIL_CLEINTE"),
        numeroPreVenda=csv_row.get("NUMERO_PRE_VENDA"),
        planoAssinatura=csv_row.get("E_PLANO_ASSINATURA"),
        canalVendas=csv_row.get("CANAL_VENDAS"),
        cartaoSempre=csv_row.get("NUMERO_CARTAO_SEMPRE"),
        parcelas=int(csv_row.get("FLDE_QT_PAR_CAR")) if csv_row.get("FLDE_QT_PAR_CAR") else None,
        tipo=csv_row.get("FLDE_TP_POS"),
        flagPagamentoUni=csv_row.get("E_PAGAMENTO_UNIFICADO"),
        nsuPos=csv_row.get("NSU_POS"),
        idDevtrocaCab=csv_row.get("ID_DEVTROCA_CAB"),
        cooPagUni=csv_row.get("COO_PAGAMENTO_UNIFICADO"),
        idEstorno=csv_row.get("ID_ESTORNO"),
        dataVenda=format_date(csv_row.get("DATA_VENDA")),
        tid=csv_row.get("T_ID"),
        codigoAutorizacao=csv_row.get("CODIGO_AUTORIZAÇÂO"),
        nomeEmpresa=csv_row.get("NOME_EMPRESA"),
    )


NEW_DICT_TO_LEGACY_DICT_MAPPING = {
    'codigoFilialOrigem': 'codigoFilialOrigem',
    'cnpjFilialOrigem': 'cnpjFilialOrigem',
    'codigoFilialDestino': 'codigoFilialDestino',
    'cnpjFilialDestino': 'cnpjFilialDestino',
    'numeroCupom': 'numeroCupom',
    'numeroCoo': 'numeroCoo',
    'nomeCliente': 'nomeCliente',
    'pdvInformado': 'pdvDevolucao',
    'numeroPedidoVTEX': 'numeroPedidoVTEX',
    'numeroPedidoDelivery': 'numeroPedidoDelivery',
    'dataMovimento': 'dataMovimento',
    'valorTotalVenda': 'valorVenda',
    'valorEstorno': 'valorEstorno',
    'dataDevolucao': 'dataDevolucao',
    'nsutef': 'codigoNSUTEF',
    'nsuhost': 'codigoNSUHOST',
    'bandeira': 'bandeira',
    'telefone': 'telefoneCliente',
    'email': 'emailCliente',
    'numeroPreVenda': 'numeroPreVenda',
    'planoAssinatura': 'planoAssinatura',
    'canalVendas': 'canalVendas',
    'cartaoSempre': 'cartaoSempre',
    'parcelas': 'quantParcelas',
    'tipo': 'tipo',
    'flagPagamentoUni': 'flagPagamentoUnificado',
    'nsuPos': 'nsuPos',
    'idDevtrocaCab': 'idTrocaCab',
    'cooPagUni': 'cooPagUni',
    'idEstorno': 'idEstorno'
}

def turn_refundlog_data_obj_into_legacy_dict(estorno_log: EstornoLog) -> Dict:
    new_version_dict = estorno_log.__dict__
    old_version_dict = {}
    
    for new_key, old_key in NEW_DICT_TO_LEGACY_DICT_MAPPING.items():
        if new_key in new_version_dict:

            if new_key == "dataMovimento" and isinstance(new_version_dict[new_key], datetime):
                new_version_dict[new_key] = new_version_dict[new_key].strftime('%d/%m/%Y')
            elif new_key == "dataMovimento" and 'T' not in new_version_dict[new_key]:
                new_version_dict[new_key] = datetime.strptime(new_version_dict[new_key], '%Y-%m-%d').strftime('%d/%m/%Y')
            elif new_key == "dataMovimento" and 'T' in new_version_dict[new_key]:
                new_version_dict[new_key] = datetime.strptime(new_version_dict[new_key], '%Y-%m-%dT%H:%M:%S').strftime('%d/%m/%Y')
            old_version_dict[old_key] = new_version_dict[new_key]
    
    return old_version_dict

def format_date(date_str: datetime) -> str:
    if date_str:
        return date_str.strftime("%Y-%m-%d")
    return None





