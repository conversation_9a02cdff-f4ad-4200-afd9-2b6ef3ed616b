import requests
import json
from datetime import datetime, timedelta
from time import perf_counter
from typing import Dict, List, Union
from services.logger import create_logger


if __name__ == '__main__': 
    vtex_service_logger = create_logger('')
    vtex_service_logger.setLevel(10)
else: 
    vtex_service_logger = create_logger(__name__, without_handler = True)



class VTEXHandler():
    
    '''
        Classe que contem métodos para lidar com informações de compras Ecommerce
        com cartão de crédito
    '''    

    def __init__(self, api_config):                
        self.api_base_url: str = api_config['api_base_url']
        self.api_key: str = api_config['api_key']
        self.api_token: str = api_config['api_token']        
        super().__init__()
        
    def get_only_estableshiment_code(self, refund_info: Dict) -> str:

        _response = self.request_with_backoff(url=self._format_url(refund_info["numeroPedidoVTEX"]), 
                                 headers=self._make_headers())                            

        # Verificando o status da requisicao
        if not _response.ok: 
            print('Erro na requisicao = {}'.format(_response.status_code))       
            return -1

        _response = json.loads(_response.text)

        # Verificando se a compra ja foi estornada authorizedDate
        if _response['status'].lower() == 'canceled':
            print('Venda cancelada - {}'.format(refund_info["numeroPedidoVTEX"]))
            return -3
        
        payments = self._avoid_giftcards(_response)
        payment_method_info = self._get_payment_method_basic_info(payments[0])
        return payment_method_info["cod_estabelecimento"]
    
    def request_with_backoff(self, url: str, headers: Dict, params: Dict=None, 
                             retries:int=4, time_limit_in_seconds:int=90)-> requests.Response:
        """
        Função que realiza uma requisição com backoff (retantativa)

        Args:
            url (str): url a ser requisitada
            headers (Dict): Cabecalhos da requisição
            params (Dict, optional): Parametros da requisição. Defaults to None.
            retries (int, optional): Numero de tentativas. Defaults to 4.
            time_limit_in_seconds (int, optional): Tempo limite em segundos. Defaults to 90.

        Returns:
            requests.Response: Resposta da requisição
        """

        start_time = perf_counter()
        time_limit_in_seconds = 90
        time_between_checks_in_seconds = time_limit_in_seconds/retries
        verifications_done = 0

        while (perf_counter() - start_time) < time_limit_in_seconds:

            if (perf_counter() - start_time) >= (verifications_done * time_between_checks_in_seconds):
                vtex_service_logger.info(f"Tentativa de requisição na VTEX {verifications_done + 1}")
                verifications_done += 1
                response = requests.get(url, headers=headers, params=params)

                if response.ok:
                    return response
                else:
                    continue
            else:
                continue

        vtex_service_logger.error(f"Não foi possível obter resposta da VTEX em até {time_limit_in_seconds} segundos.")
        return response

    def retrieve_data(self, refund_info: Dict) -> Dict:   

        _response = self.request_with_backoff(url=self._format_url(refund_info["numeroPedidoVTEX"]), 
                                 headers=self._make_headers())          

        # Verificando o status da requisicao
        if not _response.ok:      
            return -1

        _response = json.loads(_response.text)
        # refId ou sellerSku podem ser o NumeroCoo

        # Verificando se a compra ja foi estornada authorizedDate
        if _response['status'].lower() == 'canceled':
            print('Venda cancelada - {}'.format(refund_info["numeroPedidoVTEX"]))
            return -3
                          
        vtex_info, payment_method_info = self._verify_payment_methods(_response, refund_info)

        vtex_info['data_venda'] = self._convert_date(_response['creationDate'])[:10]
        vtex_info['data_autorizacao_utc'] = self._convert_date(_response['creationDate'], 0)
        vtex_info['nomeCliente'] = self._get_customer_name(_response)
        
        if _response['merchantName'] == None:
            return -8

        vtex_info['filial'] = int(_response['merchantName'].replace('PAGUEMENOS', '')) # Numero da filial

        vtex_info['numero_vtex'] = _response['marketplaceOrderId']
        _value = str(_response['value'])
        _p1 = _value[len(_value)-2:]
        _p2 = _value[:len(_value)-len(_p1)]
        _value = '{}.{}'.format(_p2, _p1)           
        vtex_info['valor_total_da_compra'] = float(_value)
    

        _first_dig = str(payment_method_info['firstDigits']) 
        _last_dig = str(payment_method_info['lastDigits'])
        vtex_info['n_cartao'] = '{}******{}'.format(_first_dig, _last_dig)
        
        vtex_info['quantidade_parcelas'] = payment_method_info['installments']        
        
        vtex_info['t_id'] = payment_method_info['tid']
       
        vtex_info['sequencial_controle'] = _response['sequence']
        if 'phone' in _response['clientProfileData']: vtex_info['telefone'] = _response['clientProfileData']['phone']
        if 'firstName' in _response['clientProfileData']: vtex_info['nome_cliente'] = _response['clientProfileData']['firstName']

        
        

        vtex_info['bandeira_cartao'] = payment_method_info['paymentSystemName'].lower() # Bandeira do cartao

        if vtex_info['bandeira_cartao'] == 'dinheiro':
            print('Compra feita a deinheiro - {}'.format(refund_info["numeroPedidoVTEX"]))
            return -4        


        if 'caixa' in vtex_info['bandeira_cartao']:
            if  'authorizationCode' in payment_method_info['connectorResponses']:
                vtex_info['codigo_autorizacao'] = payment_method_info['connectorResponses']['nsu'] 
                vtex_info['nsu_settle'] = vtex_info['codigo_autorizacao']
                vtex_info['bandeira_cartao'] = 'caixa digital'
        else:
           # Para adquirente CIELO
            if 'authorizationCode'  in payment_method_info['connectorResponses']:
                
                vtex_info['codigo_autorizacao'] = payment_method_info['connectorResponses']['authorizationCode']
                vtex_info['adquirente'] = 'cielo'    
            
            elif 'authId'  in payment_method_info['connectorResponses']:
                
                vtex_info['codigo_autorizacao'] = payment_method_info['connectorResponses']['authId']
                vtex_info['adquirente'] = 'cielo'
            else:
                vtex_info['codigo_autorizacao'] = ''

            # Para adquirente REDE
            if 'NsuSettle' in payment_method_info['connectorResponses']:

                vtex_info['nsu_settle'] = payment_method_info['connectorResponses']['NsuSettle']
                vtex_info['nsu'] = ''

            elif 'nsu' in  payment_method_info['connectorResponses']:
                
                vtex_info['nsu'] = payment_method_info['connectorResponses']['nsu']
                vtex_info['nsu_settle'] = '' # Codigo nsu, se houver (Apenas para Rede)

            elif 'tid' in payment_method_info:

                vtex_info['nsu_settle'] = payment_method_info['tid']
                vtex_info['nsu'] = payment_method_info['tid']

            else:
                vtex_info['nsu_settle'] = ''
                vtex_info['nsu'] = '' 

        if payment_method_info['group'] == 'Nubank':
            return vtex_info
            
        if 'codigo_autorizacao' not in vtex_info:
            print('####codigo de autorização não encontrado####')
            return -2        
        
        if vtex_info['codigo_autorizacao'] == '' and vtex_info['nsu'] == '' and vtex_info['nsu_settle'] == '':
            return -2        
        
        
        return vtex_info

    def retrieve_creation_date(self, order_id:str = None, seq_id:str = None, filial:str = None) -> str:

        _params = {"f_creationDate":"creationDate:[2016-01-01T02:00:00.000Z TO 2021-01-01T01:59:59.999Z]"}        
        _headers = {
            'accept': "application/json",
            'content-type': "application/json",
            'X-VTEX-API-AppToken': self.api_token,
            'X-VTEX-API-AppKey':self.api_key
        }
                
        _url = self.api_base_url

        if bool(seq_id): 
            _url = _url.format(filial, 'seq{}'.format(seq_id))                        
        else:
            order_id = self.__format_order_id(order_id)        
            _url = _url.format('', order_id)
        
        _response = self.request_with_backoff(url=_url, headers=_headers, params=_params)                               

        # Verificando o status da requisicao
        if not _response.ok: 
            print('Erro na requisicao = {}'.format(_response.status_code))       
            return -1

        _response = json.loads(_response.text)                    

        creation_date = _response['creationDate'][:10].split('-')
        
        return '{}/{}/{}'.format(creation_date[2], creation_date[1], creation_date[0])

    def __format_order_id(self, order_id:str) -> str:
        order_id = order_id.replace('-', '')
        __order_part_2 = order_id[len(order_id)-2:]
        __order_part_1 = order_id[:len(order_id)-len(__order_part_2)]
        return '{}-{}'.format(__order_part_1, __order_part_2)
    
    def _make_headers(self)-> Dict:

        headers = {
            'accept': "application/json",
            'content-type': "application/json",
            'X-VTEX-API-AppToken': self.api_token,
            'X-VTEX-API-AppKey':self.api_key
        }

        return headers
    
    def _format_url(self, order_id: str)-> str:

        order_id = self.__format_order_id(order_id)        
        url = self.api_base_url.format('', order_id)

        return url

    def _format_currency_values(self, value: int)-> float:
        return value/100
    
    def _get_customer_name(self, response: Dict)-> str:

        firstname = (str(response['clientProfileData']['firstName']))
        lastname = (str(response['clientProfileData']['lastName']))
        return str(firstname + " " + lastname)

    # 326082 
    def _verify_payment_methods(self, response: Dict, refund_info: Dict) -> Union[Dict, Dict]:

        payments = self._avoid_giftcards(response)

        if len(payments) == 1:

            payment_method_info = self._get_payment_method_basic_info(payments[0])

            return  payment_method_info, payments[0]

        vtex_service_logger.info("Venda com mais de um finalizador")
        for index in range(len(payments)): # tid

            payment_method_info = self._get_payment_method_basic_info(payments[index])

            if refund_info["bandeira"] == 'ELO CREDIT':
                bandeira = 'elo'
            else:
                bandeira = refund_info["bandeira"]
                
            if (refund_info["valorEstorno"] == payment_method_info['valor'] 
                and  bandeira.lower() == payment_method_info['bandeira_cartao'].lower()):
                
                return payment_method_info, payments[index]
        
        vtex_service_logger.warning("Sem correspondência entre o finalizador da VTEX e do troca-dev")
        vtex_service_logger.warning("O Primeiro finalizador da listagem será usado")

        payment_method_info = payment_method_info = self._get_payment_method_basic_info(payments[0])
        
        return payment_method_info, payments[0]

    def _get_payment_method_basic_info(self, payment_method: Dict) -> Dict:

        if  'authorizationCode' in payment_method["connectorResponses"]:
            auth_code = payment_method["connectorResponses"]["authorizationCode"]

        elif 'authId' in payment_method["connectorResponses"]:
            auth_code = payment_method["connectorResponses"]["authId"]
            
        else:
            auth_code = None

        payment_method_info = {
                    "valor": self._format_currency_values(payment_method["value"]),
                    "bandeira_cartao": payment_method["paymentSystemName"],
                    "codigo_autorizacao": auth_code,
                    "t_id": payment_method["tid"],
                    "cod_estabelecimento": payment_method["tid"][:10]
                }
        
        return payment_method_info


    def _update_response_with_payment_method(self, out_response: Dict, payment_method: Dict) -> Dict:
        
        if payment_method["valor"] is not None:
            out_response["valor"] = payment_method["valor"]
        
        if payment_method["codigo_autorizacao"] is not None:
            out_response["codigo_autorizacao"] = payment_method["codigo_autorizacao"]
        
        if payment_method["t_id"] is not None:
            out_response["t_id"] = payment_method["t_id"] 
            out_response["cod_estabelecimento"] = payment_method["t_id"][:10]
        else:
            out_response["cod_estabelecimento"] = out_response["t_id"][:10]

        return out_response
    
    def _convert_date(self, date_to_convert: str, utc_ref: int = 3):

        #tratando o fuso da data recebida pela vtex.
        date = date_to_convert[:10] 
        time = date_to_convert[11:19]  
        date_time = (date +" "+ time)

        date_time_obj = (datetime.strptime(date_time,'%Y-%m-%d %H:%M:%S') - timedelta(hours = utc_ref)) 
        date_time = date_time_obj.strftime('%Y-%m-%d %H:%M:%S')
        return date_time 
    
    def _avoid_giftcards(self, order_info: Dict) -> List[Dict]:

        payments = []
        transactions = order_info['paymentData']['transactions']
        for trans in transactions:
            for payment in trans["payments"]:
                if payment["giftCardId"] is None:
                    payments.append(payment)

        return payments


        