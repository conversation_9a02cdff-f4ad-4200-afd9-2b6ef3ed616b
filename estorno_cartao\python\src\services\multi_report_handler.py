import requests
from services.logger import create_logger

logger = create_logger(__name__, without_handler = True)


bot_token = "5344840058:AAE3u0djRF-e8pd6RN9FvL0XamS7BG9sp6Y"
chat_id = -616729707

def log_report(msg: str, log_level_name: str = None):
    """Envia mensagens de logs para várias platformas
    Comportamento padrão:
        Enviar para console e telegram


    Args:
        msg (str): menssagem a ser enviada
        log_level_name (str, optional): Nivel de log da messagem. Caso não for informado
        será enviado a mensagem somente para o telegram.
    """

    data = {"chat_id": chat_id, "text": msg}
    url = "https://api.telegram.org/bot{}/sendMessage".format(bot_token)
        

    if log_level_name == "info" or log_level_name is None: logger.info(msg)
        
    elif log_level_name == "warning": logger.warning(msg)
        
    elif log_level_name == "error": logger.error(msg)
    
    else: logger.warning(f"Level de log {log_level_name} não mapeado")
        
    try:
        requests.post(url, data)
    except Exception as e:
        logger.error(f"Erro ao enviar mensagem para o telegram: {e}")
